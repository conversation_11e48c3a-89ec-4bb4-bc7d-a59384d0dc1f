import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Patch,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpCode,
  UseGuards,
  UseInterceptors,
  Logger,
  Version,
  ParseIntPipe,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/shared/infrastructure/guards/jwt-auth.guard';
import { CurrentUser } from '@/shared/infrastructure/decorators/current-user.decorator';
import { CqrsUserContextInterceptor } from '@/modules/crm/infrastructure/interceptors/cqrs-user-context.interceptor';

// Commands
import { CreateLeadCommand } from '@/modules/crm/application/cqrs/commands/create-lead.command';
import { ConvertLeadToOpportunityCommand } from '@/modules/crm/application/cqrs/commands/convert-lead-to-opportunity.command';
import { UpdateLeadCommand } from '@/modules/crm/application/cqrs/commands/update-lead.command';
import { DeleteLeadCommand } from '@/modules/crm/application/cqrs/commands/delete-lead.command';
import { AssignLeadToUserCommand } from '@/modules/crm/application/cqrs/commands/assign-lead-to-user.command';
import { AssignLeadToTeamCommand } from '@/modules/crm/application/cqrs/commands/assign-lead-to-team.command';
import { UpdateLeadPriorityCommand } from '@/modules/crm/application/cqrs/commands/handlers/update-lead-priority.handler';
import { UpdateRevenueForecastCommand } from '@/modules/crm/application/cqrs/commands/update-revenue-forecast.command';
import { SetLeadDeadlineCommand } from '@/modules/crm/application/cqrs/commands/set-lead-deadline.command';
import { AddLeadTagCommand } from '@/modules/crm/application/cqrs/commands/add-lead-tag.command';
import { RemoveLeadTagCommand } from '@/modules/crm/application/cqrs/commands/remove-lead-tag.command';

// Queries
import { GetPipelineAnalyticsQuery } from '@/modules/crm/application/cqrs/queries/get-pipeline-analytics.query';
import { GetLeadByIdQuery } from '@/modules/crm/application/cqrs/queries/get-lead-by-id.query';
import { GetLeadsByFiltersQuery, LeadFilters } from '@/modules/crm/application/cqrs/queries/get-leads-by-filters.query';
import { GetLeadStatisticsQuery } from '@/modules/crm/application/cqrs/queries/get-lead-statistics.query';
import { GetOverdueLeadsQuery } from '@/modules/crm/application/cqrs/queries/get-overdue-leads.query';
import { GetLeadsRequiringAttentionQuery } from '@/modules/crm/application/cqrs/queries/get-leads-requiring-attention.query';
import { SearchLeadsQuery } from '@/modules/crm/application/cqrs/queries/search-leads.query';

// Value Objects
import { LeadPriority } from '@/modules/crm/domain/value-objects/lead-priority.vo';
import { LeadType } from '@/modules/crm/domain/value-objects/lead-type.vo';

// DTOs
import {
  SearchLeadsDto,
  UpdateLeadDto,
  UpdatePriorityDto,
  AssignToUserDto,
  AssignToTeamDto,
  UpdateForecastDto,
  SetDeadlineDto,
  AddTagDto,
} from '@/modules/crm/presentation/dto/cqrs-request.dto';

// DTOs and Response Types
type CreateLeadCqrsDto = {
  name: string;
  email?: string;
  phone?: string;
  company?: string;
  website?: string;
  address?: string;
  city?: string;
  country?: string;
  description?: string;
  priority?: number;
  type?: string;
  source?: string;
  expectedRevenue?: number;
  probability?: number;
  assignedUserId?: number;
  teamId?: number;
  campaignId?: number;
  sourceId?: number;
  mediumId?: number;
  tags?: string[];
  dateDeadline?: string;
}

type ConvertLeadCqrsDto = {
  opportunityName?: string;
  expectedRevenue: number;
  probability?: number;
  closeDate?: string;
  partnerId?: number;
  stageId?: number;
  assignedUserId?: number;
  dateDeadline?: string;
  description?: string;
  reason?: string;
}

type CreateLeadCqrsResponse = {
  success: boolean;
  message: string;
  data: {
    leadId: string;
    lead: any;
  };
  metadata: {
    commandType: string;
    executedAt: string;
    executedBy: string;
  };
}

type ConvertLeadCqrsResponse = {
  success: boolean;
  message: string;
  data: {
    leadId: number;
    opportunityId: string;
    opportunity: any;
  };
  metadata: {
    commandType: string;
    executedAt: string;
    executedBy: string;
  };
}

type PipelineAnalyticsCqrsResponse = {
  success: boolean;
  message: string;
  data: any;
  metadata: {
    queryType: string;
    executedAt: string;
    executedBy: string;
    cacheKey?: string;
    [key: string]: any;
  };
}

type PipelineAnalyticsCqrsQueryDto = {
  teamId?: number;
  userId?: number;
  dateFrom?: string;
  dateTo?: string;
  includeStageMetrics?: boolean;
  includeConversionRates?: boolean;
  includeBottlenecks?: boolean;
  includeForecast?: boolean;
  includeComparisons?: boolean;
  stageIds?: number[];
  priorityLevels?: string[];
  sources?: string[];
  groupBy?: 'stage' | 'team' | 'user' | 'source' | 'priority';
  timeGranularity?: 'day' | 'week' | 'month' | 'quarter';
}

/**
 * CQRS-based Leads Controller
 * Demonstrates the new CQRS pattern implementation
 */
@ApiTags('CRM - Leads (CQRS)')
@Controller({ path: 'crm/leads-cqrs', version: '1' })
@UseGuards(JwtAuthGuard)
// @UseInterceptors(CqrsUserContextInterceptor) // Temporarily disabled
@ApiBearerAuth()
export class LeadsCqrsController {
  private readonly logger = new Logger(LeadsCqrsController.name);

  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
  ) {}

  /**
   * Test endpoint to verify controller is working
   */
  @Get('test')
  @HttpCode(HttpStatus.OK)
  test() {
    return {
      success: true,
      message: 'LeadsCqrsController is working!',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Create a new lead using CQRS command
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new lead (CQRS)' })
  @ApiResponse({ status: 201, description: 'Lead created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async createLead(
    @Body() createLeadDto: CreateLeadCqrsDto,
    @CurrentUser() user: any = { id: 1, name: 'Test User' }, // Default user for testing
  ): Promise<CreateLeadCqrsResponse> {
    this.logger.log(`Creating lead: ${createLeadDto.name} by user: ${user?.id || 'anonymous'}`);

    try {
      // Map DTO to command
      const command = new CreateLeadCommand(
        createLeadDto.name,
        createLeadDto.email,
        createLeadDto.phone,
        createLeadDto.company,
        createLeadDto.website,
        createLeadDto.address,
        createLeadDto.city,
        createLeadDto.country,
        createLeadDto.source || 'website',
        createLeadDto.type ? LeadType.fromValue(createLeadDto.type) : LeadType.LEAD,
        createLeadDto.priority ? LeadPriority.fromValue(createLeadDto.priority) : LeadPriority.MEDIUM,
        createLeadDto.expectedRevenue,
        createLeadDto.probability,
        createLeadDto.description,
        createLeadDto.assignedUserId,
        createLeadDto.teamId,
        createLeadDto.campaignId,
        createLeadDto.sourceId,
        createLeadDto.mediumId,
        createLeadDto.tags || [],
        createLeadDto.dateDeadline ? new Date(createLeadDto.dateDeadline) : undefined,
        user?.id || 1, // createdBy
        user?.companyId || 1,
      );

      // Execute command
      const result = await this.commandBus.execute(command);

      return {
        success: true,
        message: 'Lead created successfully',
        data: {
          leadId: result.leadId,
          lead: result.lead.toPlainObject(),
        },
        metadata: {
          commandType: 'CreateLeadCommand',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to create lead: ${createLeadDto.name}`, error);
      throw error;
    }
  }

  /**
   * Convert lead to opportunity using CQRS command
   */
  @Post(':id/convert-to-opportunity')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Convert lead to opportunity (CQRS)' })
  @ApiResponse({ status: 200, description: 'Lead converted successfully' })
  @ApiResponse({ status:404, description: 'Lead not found' })
  @ApiResponse({ status: 400, description: 'Lead cannot be converted' })
  async convertToOpportunity(
    @Param('id') leadId: number,
    @Body() convertDto: ConvertLeadCqrsDto,
    @CurrentUser() user: any = { id: 1, name: 'Test User' }, // Default user for testing
  ): Promise<ConvertLeadCqrsResponse> {
    this.logger.log(`Converting lead ${leadId} to opportunity by user: ${user?.id || 'anonymous'}`);

    try {
      // Create command
      const command = new ConvertLeadToOpportunityCommand(
        leadId,
        convertDto.expectedRevenue,
        convertDto.probability || 10,
        convertDto.partnerId,
        convertDto.stageId,
        convertDto.assignedUserId,
        convertDto.dateDeadline ? new Date(convertDto.dateDeadline) : undefined,
        convertDto.description,
        user?.id || 1, // convertedBy
        convertDto.reason,
      );

      // Execute command
      const result = await this.commandBus.execute(command);

      return {
        success: true,
        message: 'Lead converted to opportunity successfully',
        data: {
          leadId,
          opportunityId: result.opportunityId,
          opportunity: result.opportunity.toPlainObject(),
        },
        metadata: {
          commandType: 'ConvertLeadToOpportunityCommand',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to convert lead ${leadId} to opportunity`, error);
      throw error;
    }
  }

  /**
   * Get pipeline analytics using CQRS query
   */
  @Get('analytics/pipeline')
  @ApiOperation({ summary: 'Get pipeline analytics (CQRS)' })
  @ApiResponse({ status: 200, description: 'Pipeline analytics retrieved successfully' })
  async getPipelineAnalytics(
    @Query() analyticsQuery: PipelineAnalyticsCqrsQueryDto,
    @CurrentUser() user: any = { id: 1, name: 'Test User' }, // Default user for testing
  ): Promise<PipelineAnalyticsCqrsResponse> {
    this.logger.log(`Getting pipeline analytics for user: ${user?.id || 'anonymous'}`);

    try {
      // Create query
      const query = new GetPipelineAnalyticsQuery(
        analyticsQuery.teamId,
        analyticsQuery.userId,
        analyticsQuery.dateFrom ? new Date(analyticsQuery.dateFrom) : undefined,
        analyticsQuery.dateTo ? new Date(analyticsQuery.dateTo) : undefined,
        analyticsQuery.includeStageMetrics !== false,
        analyticsQuery.includeConversionRates !== false,
        analyticsQuery.includeBottlenecks !== false,
        analyticsQuery.includeForecast !== false,
        analyticsQuery.includeComparisons || false,
        analyticsQuery.stageIds,
        analyticsQuery.priorityLevels,
        analyticsQuery.sources,
        analyticsQuery.groupBy,
        analyticsQuery.timeGranularity,
        user?.id || 1, // requestedBy
      );

      // Execute query
      const result = await this.queryBus.execute(query);

      return {
        success: true,
        message: 'Pipeline analytics retrieved successfully',
        data: result,
        metadata: {
          queryType: 'GetPipelineAnalyticsQuery',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
          cacheKey: query.getCacheKey(),
        },
      };

    } catch (error) {
      this.logger.error('Failed to get pipeline analytics', error);
      throw error;
    }
  }

  /**
   * Get lead by ID using CQRS query
   */
  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get lead by ID (CQRS)' })
  @ApiParam({ name: 'id', description: 'Lead ID', type: 'number' })
  @ApiResponse({ status: 200, description: 'Lead retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Lead not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getLeadById(
    @Param('id', ParseIntPipe) leadId: number,
    @CurrentUser() user: any = { id: 1, name: 'Test User' },
  ): Promise<any> {
    this.logger.log(`Getting lead by ID: ${leadId} by user: ${user?.id || 'anonymous'}`);

    try {
      const query = new GetLeadByIdQuery(leadId);
      const lead = await this.queryBus.execute(query);

      if (!lead) {
        return {
          success: false,
          message: 'Lead not found',
          statusCode: 404,
        };
      }

      return {
        success: true,
        message: 'Lead retrieved successfully',
        data: lead,
        metadata: {
          queryType: 'GetLeadByIdQuery',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
          leadId,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to get lead by ID: ${leadId}`, error);
      throw error;
    }
  }

  /**
   * Get leads by filters using CQRS query
   */
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get leads by filters (CQRS)' })
  @ApiQuery({ name: 'status', required: false, description: 'Lead status filter' })
  @ApiQuery({ name: 'priority', required: false, description: 'Lead priority filter' })
  @ApiQuery({ name: 'assignedUserId', required: false, type: 'number', description: 'Assigned user ID filter' })
  @ApiQuery({ name: 'teamId', required: false, type: 'number', description: 'Team ID filter' })
  @ApiQuery({ name: 'source', required: false, description: 'Lead source filter' })
  @ApiQuery({ name: 'limit', required: false, type: 'number', description: 'Limit results (default: 20)' })
  @ApiQuery({ name: 'offset', required: false, type: 'number', description: 'Offset results (default: 0)' })
  @ApiResponse({ status: 200, description: 'Leads retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getLeadsByFilters(
    @Query('status') status?: string,
    @Query('priority') priority?: string,
    @Query('assignedUserId') assignedUserId?: number,
    @Query('teamId') teamId?: number,
    @Query('source') source?: string,
    @Query('limit') limit: number = 20,
    @Query('offset') offset: number = 0,
    @CurrentUser() user: any = { id: 1, name: 'Test User' },
  ): Promise<any> {
    this.logger.log(`Getting leads by filters by user: ${user?.id || 'anonymous'}`);

    try {
      const filters: LeadFilters = {
        status,
        priority,
        assignedUserId,
        teamId,
        source,
        limit,
        offset,
      };

      const query = new GetLeadsByFiltersQuery(filters);
      const result = await this.queryBus.execute(query);

      return {
        success: true,
        message: 'Leads retrieved successfully',
        data: result.leads,
        pagination: {
          total: result.total,
          limit,
          offset,
          hasMore: result.hasMore,
        },
        metadata: {
          queryType: 'GetLeadsByFiltersQuery',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
          filters,
          cached: result.cached,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to get leads by filters`, error);
      throw error;
    }
  }

  /**
   * Get lead statistics using CQRS query
   */
  @Get('statistics')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get lead statistics (CQRS)' })
  @ApiQuery({ name: 'status', required: false, description: 'Status filter for statistics' })
  @ApiQuery({ name: 'teamId', required: false, type: 'number', description: 'Team ID filter for statistics' })
  @ApiQuery({ name: 'assignedUserId', required: false, type: 'number', description: 'Assigned user ID filter for statistics' })
  @ApiResponse({ status: 200, description: 'Lead statistics retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getLeadStatistics(
    @Query('status') status?: string,
    @Query('teamId') teamId?: number,
    @Query('assignedUserId') assignedUserId?: number,
    @CurrentUser() user: any = { id: 1, name: 'Test User' },
  ): Promise<any> {
    this.logger.log(`Getting lead statistics by user: ${user?.id || 'anonymous'}`);

    try {
      const filters: LeadFilters = {
        status,
        teamId,
        assignedUserId,
      };

      const query = new GetLeadStatisticsQuery(filters);
      const statistics = await this.queryBus.execute(query);

      return {
        success: true,
        message: 'Lead statistics retrieved successfully',
        data: statistics,
        metadata: {
          queryType: 'GetLeadStatisticsQuery',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
          filters,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to get lead statistics`, error);
      throw error;
    }
  }

  /**
   * Get overdue leads using CQRS query
   */
  @Get('overdue')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get overdue leads (CQRS)' })
  @ApiQuery({ name: 'teamId', required: false, type: 'number', description: 'Team ID filter' })
  @ApiQuery({ name: 'userId', required: false, type: 'number', description: 'User ID filter' })
  @ApiResponse({ status: 200, description: 'Overdue leads retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getOverdueLeads(
    @Query('teamId') teamId?: number,
    @Query('userId') userId?: number,
    @CurrentUser() user: any = { id: 1, name: 'Test User' },
  ): Promise<any> {
    this.logger.log(`Getting overdue leads by user: ${user?.id || 'anonymous'}`);

    try {
      const query = new GetOverdueLeadsQuery(teamId, userId);
      const leads = await this.queryBus.execute(query);

      return {
        success: true,
        message: 'Overdue leads retrieved successfully',
        data: leads,
        metadata: {
          queryType: 'GetOverdueLeadsQuery',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
          filters: { teamId, userId },
        },
      };

    } catch (error) {
      this.logger.error(`Failed to get overdue leads`, error);
      throw error;
    }
  }

  /**
   * Get leads requiring attention using CQRS query
   */
  @Get('attention-required')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get leads requiring attention (CQRS)' })
  @ApiQuery({ name: 'teamId', required: false, type: 'number', description: 'Team ID filter' })
  @ApiQuery({ name: 'userId', required: false, type: 'number', description: 'User ID filter' })
  @ApiResponse({ status: 200, description: 'Leads requiring attention retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getLeadsRequiringAttention(
    @Query('teamId') teamId?: number,
    @Query('userId') userId?: number,
    @CurrentUser() user: any = { id: 1, name: 'Test User' },
  ): Promise<any> {
    this.logger.log(`Getting leads requiring attention by user: ${user?.id || 'anonymous'}`);

    try {
      const query = new GetLeadsRequiringAttentionQuery(teamId, userId);
      const leads = await this.queryBus.execute(query);

      return {
        success: true,
        message: 'Leads requiring attention retrieved successfully',
        data: leads,
        metadata: {
          queryType: 'GetLeadsRequiringAttentionQuery',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
          filters: { teamId, userId },
        },
      };

    } catch (error) {
      this.logger.error(`Failed to get leads requiring attention`, error);
      throw error;
    }
  }

  /**
   * Search leads using CQRS query
   */
  @Post('search')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Search leads (CQRS)' })
  @ApiResponse({ status: 200, description: 'Leads search completed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid search parameters' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async searchLeads(
    @Body() searchDto: SearchLeadsDto,
    @CurrentUser() user: any = { id: 1, name: 'Test User' },
  ): Promise<any> {
    this.logger.log(`Searching leads with term: "${searchDto.searchTerm}" by user: ${user?.id || 'anonymous'}`);

    try {
      const query = new SearchLeadsQuery(
        searchDto.searchTerm,
        searchDto.filters,
        searchDto.limit,
      );
      const leads = await this.queryBus.execute(query);

      return {
        success: true,
        message: 'Leads search completed successfully',
        data: leads,
        metadata: {
          queryType: 'SearchLeadsQuery',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
          searchTerm: searchDto.searchTerm,
          filters: searchDto.filters,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to search leads with term: "${searchDto.searchTerm}"`, error);
      throw error;
    }
  }

  /**
   * Update lead using CQRS command
   */
  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Update lead (CQRS)' })
  @ApiParam({ name: 'id', description: 'Lead ID', type: 'number' })
  @ApiResponse({ status: 200, description: 'Lead updated successfully' })
  @ApiResponse({ status: 404, description: 'Lead not found' })
  @ApiResponse({ status: 400, description: 'Invalid update data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async updateLead(
    @Param('id', ParseIntPipe) leadId: number,
    @Body() updateDto: UpdateLeadDto,
    @CurrentUser() user: any = { id: 1, name: 'Test User' },
  ): Promise<any> {
    this.logger.log(`Updating lead ${leadId} by user: ${user?.id || 'anonymous'}`);

    try {
      const command = new UpdateLeadCommand(leadId, updateDto);
      const result = await this.commandBus.execute(command);

      return {
        success: true,
        message: 'Lead updated successfully',
        data: result,
        metadata: {
          commandType: 'UpdateLeadCommand',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
          leadId,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to update lead ${leadId}`, error);
      throw error;
    }
  }

  /**
   * Delete lead using CQRS command
   */
  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Delete lead (CQRS)' })
  @ApiParam({ name: 'id', description: 'Lead ID', type: 'number' })
  @ApiResponse({ status: 200, description: 'Lead deleted successfully' })
  @ApiResponse({ status: 404, description: 'Lead not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async deleteLead(
    @Param('id', ParseIntPipe) leadId: number,
    @CurrentUser() user: any = { id: 1, name: 'Test User' },
  ): Promise<any> {
    this.logger.log(`Deleting lead ${leadId} by user: ${user?.id || 'anonymous'}`);

    try {
      const command = new DeleteLeadCommand(leadId);
      await this.commandBus.execute(command);

      return {
        success: true,
        message: 'Lead deleted successfully',
        data: { leadId },
        metadata: {
          commandType: 'DeleteLeadCommand',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
          leadId,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to delete lead ${leadId}`, error);
      throw error;
    }
  }

  /**
   * Update lead priority using CQRS command
   */
  @Patch(':id/priority')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Update lead priority (CQRS)' })
  @ApiParam({ name: 'id', description: 'Lead ID', type: 'number' })
  @ApiResponse({ status: 200, description: 'Lead priority updated successfully' })
  @ApiResponse({ status: 404, description: 'Lead not found' })
  @ApiResponse({ status: 400, description: 'Invalid priority value' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async updateLeadPriority(
    @Param('id', ParseIntPipe) leadId: number,
    @Body() priorityDto: UpdatePriorityDto,
    @CurrentUser() user: any = { id: 1, name: 'Test User' },
  ): Promise<any> {
    this.logger.log(`Updating priority for lead ${leadId} by user: ${user?.id || 'anonymous'}`);

    try {
      const command = new UpdateLeadPriorityCommand(leadId, priorityDto.priority);
      const result = await this.commandBus.execute(command);

      return {
        success: true,
        message: 'Lead priority updated successfully',
        data: result,
        metadata: {
          commandType: 'UpdateLeadPriorityCommand',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
          leadId,
          newPriority: priorityDto.priority,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to update priority for lead ${leadId}`, error);
      throw error;
    }
  }

  /**
   * Assign lead to user using CQRS command
   */
  @Patch(':id/assign-user')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Assign lead to user (CQRS)' })
  @ApiParam({ name: 'id', description: 'Lead ID', type: 'number' })
  @ApiResponse({ status: 200, description: 'Lead assigned to user successfully' })
  @ApiResponse({ status: 404, description: 'Lead not found' })
  @ApiResponse({ status: 400, description: 'Invalid user assignment data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async assignLeadToUser(
    @Param('id', ParseIntPipe) leadId: number,
    @Body() assignDto: AssignToUserDto,
    @CurrentUser() user: any = { id: 1, name: 'Test User' },
  ): Promise<any> {
    this.logger.log(`Assigning lead ${leadId} to user ${assignDto.userId} by user: ${user?.id || 'anonymous'}`);

    try {
      const command = new AssignLeadToUserCommand(leadId, assignDto.userId, user.id);
      await this.commandBus.execute(command);

      return {
        success: true,
        message: 'Lead assigned to user successfully',
        data: { leadId, assignedUserId: assignDto.userId },
        metadata: {
          commandType: 'AssignLeadToUserCommand',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
          leadId,
          assignedUserId: assignDto.userId,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to assign lead ${leadId} to user ${assignDto.userId}`, error);
      throw error;
    }
  }

  /**
   * Assign lead to team using CQRS command
   */
  @Patch(':id/assign-team')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Assign lead to team (CQRS)' })
  @ApiParam({ name: 'id', description: 'Lead ID', type: 'number' })
  @ApiResponse({ status: 200, description: 'Lead assigned to team successfully' })
  @ApiResponse({ status: 404, description: 'Lead not found' })
  @ApiResponse({ status: 400, description: 'Invalid team assignment data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async assignLeadToTeam(
    @Param('id', ParseIntPipe) leadId: number,
    @Body() assignDto: AssignToTeamDto,
    @CurrentUser() user: any = { id: 1, name: 'Test User' },
  ): Promise<any> {
    this.logger.log(`Assigning lead ${leadId} to team ${assignDto.teamId} by user: ${user?.id || 'anonymous'}`);

    try {
      const command = new AssignLeadToTeamCommand(leadId, assignDto.teamId, user.id);
      await this.commandBus.execute(command);

      return {
        success: true,
        message: 'Lead assigned to team successfully',
        data: { leadId, assignedTeamId: assignDto.teamId },
        metadata: {
          commandType: 'AssignLeadToTeamCommand',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
          leadId,
          assignedTeamId: assignDto.teamId,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to assign lead ${leadId} to team ${assignDto.teamId}`, error);
      throw error;
    }
  }

  /**
   * Update revenue forecast using CQRS command
   */
  @Patch(':id/forecast')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Update lead revenue forecast (CQRS)' })
  @ApiParam({ name: 'id', description: 'Lead ID', type: 'number' })
  @ApiResponse({ status: 200, description: 'Revenue forecast updated successfully' })
  @ApiResponse({ status: 404, description: 'Lead not found' })
  @ApiResponse({ status: 400, description: 'Invalid forecast data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async updateRevenueForecast(
    @Param('id', ParseIntPipe) leadId: number,
    @Body() forecastDto: UpdateForecastDto,
    @CurrentUser() user: any = { id: 1, name: 'Test User' },
  ): Promise<any> {
    this.logger.log(`Updating revenue forecast for lead ${leadId} by user: ${user?.id || 'anonymous'}`);

    try {
      const command = new UpdateRevenueForecastCommand(
        leadId,
        forecastDto.expectedRevenue,
        forecastDto.probability,
      );
      const result = await this.commandBus.execute(command);

      return {
        success: true,
        message: 'Revenue forecast updated successfully',
        data: result,
        metadata: {
          commandType: 'UpdateRevenueForecastCommand',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
          leadId,
          expectedRevenue: forecastDto.expectedRevenue,
          probability: forecastDto.probability,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to update revenue forecast for lead ${leadId}`, error);
      throw error;
    }
  }

  /**
   * Set lead deadline using CQRS command
   */
  @Patch(':id/deadline')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Set lead deadline (CQRS)' })
  @ApiParam({ name: 'id', description: 'Lead ID', type: 'number' })
  @ApiResponse({ status: 200, description: 'Lead deadline set successfully' })
  @ApiResponse({ status: 404, description: 'Lead not found' })
  @ApiResponse({ status: 400, description: 'Invalid deadline data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async setLeadDeadline(
    @Param('id', ParseIntPipe) leadId: number,
    @Body() deadlineDto: SetDeadlineDto,
    @CurrentUser() user: any = { id: 1, name: 'Test User' },
  ): Promise<any> {
    this.logger.log(`Setting deadline for lead ${leadId} by user: ${user?.id || 'anonymous'}`);

    try {
      const command = new SetLeadDeadlineCommand(leadId, new Date(deadlineDto.deadline));
      const result = await this.commandBus.execute(command);

      return {
        success: true,
        message: 'Lead deadline set successfully',
        data: result,
        metadata: {
          commandType: 'SetLeadDeadlineCommand',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
          leadId,
          deadline: deadlineDto.deadline,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to set deadline for lead ${leadId}`, error);
      throw error;
    }
  }

  /**
   * Add tag to lead using CQRS command
   */
  @Post(':id/tags')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Add tag to lead (CQRS)' })
  @ApiParam({ name: 'id', description: 'Lead ID', type: 'number' })
  @ApiResponse({ status: 200, description: 'Tag added to lead successfully' })
  @ApiResponse({ status: 404, description: 'Lead not found' })
  @ApiResponse({ status: 400, description: 'Invalid tag data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async addLeadTag(
    @Param('id', ParseIntPipe) leadId: number,
    @Body() tagDto: AddTagDto,
    @CurrentUser() user: any = { id: 1, name: 'Test User' },
  ): Promise<any> {
    this.logger.log(`Adding tag to lead ${leadId} by user: ${user?.id || 'anonymous'}`);

    try {
      const command = new AddLeadTagCommand(leadId, tagDto.tag);
      await this.commandBus.execute(command);

      return {
        success: true,
        message: 'Tag added to lead successfully',
        data: { leadId, tag: tagDto.tag },
        metadata: {
          commandType: 'AddLeadTagCommand',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
          leadId,
          tag: tagDto.tag,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to add tag to lead ${leadId}`, error);
      throw error;
    }
  }

  /**
   * Remove tag from lead using CQRS command
   */
  @Delete(':id/tags/:tag')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Remove tag from lead (CQRS)' })
  @ApiParam({ name: 'id', description: 'Lead ID', type: 'number' })
  @ApiParam({ name: 'tag', description: 'Tag name', type: 'string' })
  @ApiResponse({ status: 200, description: 'Tag removed from lead successfully' })
  @ApiResponse({ status: 404, description: 'Lead or tag not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async removeLeadTag(
    @Param('id', ParseIntPipe) leadId: number,
    @Param('tag') tag: string,
    @CurrentUser() user: any = { id: 1, name: 'Test User' },
  ): Promise<any> {
    this.logger.log(`Removing tag ${tag} from lead ${leadId} by user: ${user?.id || 'anonymous'}`);

    try {
      const command = new RemoveLeadTagCommand(leadId, tag);
      await this.commandBus.execute(command);

      return {
        success: true,
        message: 'Tag removed from lead successfully',
        data: { leadId, tag },
        metadata: {
          commandType: 'RemoveLeadTagCommand',
          executedAt: new Date().toISOString(),
          executedBy: user.id,
          leadId,
          tag,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to remove tag ${tag} from lead ${leadId}`, error);
      throw error;
    }
  }
}

// DTOs for request/response
interface CreateLeadDto {
  name: string;
  email?: string;
  phone?: string;
  company?: string;
  website?: string;
  address?: string;
  city?: string;
  country?: string;
  source?: string;
  type?: string;
  priority?: number;
  expectedRevenue?: number;
  probability?: number;
  description?: string;
  assignedUserId?: number;
  teamId?: number;
  campaignId?: number;
  sourceId?: number;
  mediumId?: number;
  tags?: string[];
  dateDeadline?: string;
}

interface ConvertLeadDto {
  expectedRevenue: number;
  probability?: number;
  partnerId?: number;
  stageId?: number;
  assignedUserId?: number;
  dateDeadline?: string;
  description?: string;
  reason?: string;
}

interface PipelineAnalyticsQueryDto {
  teamId?: number;
  userId?: number;
  dateFrom?: string;
  dateTo?: string;
  includeStageMetrics?: boolean;
  includeConversionRates?: boolean;
  includeBottlenecks?: boolean;
  includeForecast?: boolean;
  includeComparisons?: boolean;
  stageIds?: number[];
  priorityLevels?: string[];
  sources?: string[];
  groupBy?: 'stage' | 'team' | 'user' | 'source' | 'priority';
  timeGranularity?: 'day' | 'week' | 'month' | 'quarter';
}

interface CreateLeadResponse {
  success: boolean;
  message: string;
  data: {
    leadId: number;
    lead: any;
  };
  metadata: any;
}

interface ConvertLeadResponse {
  success: boolean;
  message: string;
  data: {
    leadId: number;
    opportunityId: number;
    opportunity: any;
  };
  metadata: any;
}

interface PipelineAnalyticsResponse {
  success: boolean;
  message: string;
  data: any;
  metadata: any;
}
