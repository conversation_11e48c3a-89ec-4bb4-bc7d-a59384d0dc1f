import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsDateString, IsArray, IsEnum, Min, Max } from 'class-validator';
import { LeadPriority } from '@/modules/crm/domain/value-objects/lead-priority.vo';
import { LeadFilters } from '@/modules/crm/application/cqrs/queries/get-leads-by-filters.query';

/**
 * DTO for searching leads
 */
export class SearchLeadsDto {
  @ApiProperty({ description: 'Search term to look for in lead data' })
  @IsString()
  searchTerm: string;

  @ApiProperty({ description: 'Filters to apply to search', required: false })
  @IsOptional()
  filters?: LeadFilters;

  @ApiProperty({ description: 'Maximum number of results to return', default: 20, required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;
}

/**
 * DTO for updating a lead
 */
export class UpdateLeadDto {
  @ApiProperty({ description: 'Lead name', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: 'Lead email', required: false })
  @IsOptional()
  @IsString()
  email?: string;

  @ApiProperty({ description: 'Lead phone number', required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({ description: 'Partner name', required: false })
  @IsOptional()
  @IsString()
  partnerName?: string;

  @ApiProperty({ description: 'Lead description', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Expected revenue', required: false })
  @IsOptional()
  @IsNumber()
  expectedRevenue?: number;

  @ApiProperty({ description: 'Probability percentage', required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  probability?: number;

  @ApiProperty({ description: 'Lead priority (0=Low, 1=Medium, 2=High, 3=Very High)', enum: [0, 1, 2, 3], required: false })
  @IsOptional()
  @IsNumber()
  priority?: number;

  @ApiProperty({ description: 'Stage ID', required: false })
  @IsOptional()
  @IsNumber()
  stageId?: number;

  @ApiProperty({ description: 'User ID to assign lead to', required: false })
  @IsOptional()
  @IsNumber()
  userId?: number;

  @ApiProperty({ description: 'Team ID to assign lead to', required: false })
  @IsOptional()
  @IsNumber()
  teamId?: number;

  @ApiProperty({ description: 'Lead deadline', required: false })
  @IsOptional()
  @IsDateString()
  deadline?: string;

  @ApiProperty({ description: 'Tag IDs to assign to lead', required: false })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  tagIds?: number[];
}

/**
 * DTO for updating lead priority
 */
export class UpdatePriorityDto {
  @ApiProperty({ description: 'New priority level (0=Low, 1=Medium, 2=High, 3=Very High)', enum: [0, 1, 2, 3] })
  @IsNumber()
  priority: number;
}

/**
 * DTO for assigning lead to user
 */
export class AssignToUserDto {
  @ApiProperty({ description: 'User ID to assign lead to' })
  @IsNumber()
  userId: number;
}

/**
 * DTO for assigning lead to team
 */
export class AssignToTeamDto {
  @ApiProperty({ description: 'Team ID to assign lead to' })
  @IsNumber()
  teamId: number;
}

/**
 * DTO for updating revenue forecast
 */
export class UpdateForecastDto {
  @ApiProperty({ description: 'Expected revenue amount' })
  @IsNumber()
  @Min(0)
  expectedRevenue: number;

  @ApiProperty({ description: 'Probability percentage' })
  @IsNumber()
  @Min(0)
  @Max(100)
  probability: number;
}

/**
 * DTO for setting lead deadline
 */
export class SetDeadlineDto {
  @ApiProperty({ description: 'Deadline date in ISO format' })
  @IsDateString()
  deadline: string;
}

/**
 * DTO for adding tag to lead
 */
export class AddTagDto {
  @ApiProperty({ description: 'Tag name to add to lead' })
  @IsString()
  tag: string;
}
