import { Query<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { GetLeadByIdQuery } from '../get-lead-by-id.query';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { LEAD_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';
import { CacheService } from '@/shared/infrastructure/cache';

/**
 * Get Lead by ID Query Handler
 * Handles retrieving a lead by its ID with caching support
 */
@Injectable()
@QueryHandler(GetLeadByIdQuery)
export class GetLeadByIdHandler implements IQueryHandler<GetLeadByIdQuery> {
  private readonly logger = new Logger(GetLeadByIdHandler.name);
  private readonly CACHE_TTL = 300; // 5 minutes
  private readonly CACHE_PREFIX = 'lead:';

  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
    private readonly cacheService: CacheService,
  ) {}

  async execute(query: GetLeadByIdQuery): Promise<Lead | null> {
    const { leadId } = query;

    try {
      this.logger.log(`Executing GetLeadByIdQuery for lead ID: ${leadId}`);

      // Check cache first
      const cacheKey = `${this.CACHE_PREFIX}${leadId}`;
      const cachedLead = await this.cacheService.get<Lead>(cacheKey);

      if (cachedLead) {
        this.logger.log(`Lead ${leadId} found in cache`);
        return cachedLead;
      }

      // Fetch from repository
      const lead = await this.leadRepository.findById(leadId);

      if (!lead) {
        this.logger.warn(`Lead with ID ${leadId} not found`);
        return null;
      }

      // Cache the result
      await this.cacheService.set(cacheKey, lead, this.CACHE_TTL);

      this.logger.log(`Lead ${leadId} retrieved successfully and cached`);
      return lead;

    } catch (error) {
      this.logger.error(`Failed to execute GetLeadByIdQuery for lead ID: ${leadId}`, error);
      throw error;
    }
  }
}
