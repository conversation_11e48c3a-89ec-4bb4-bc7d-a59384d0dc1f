import { Query<PERSON><PERSON><PERSON>, <PERSON><PERSON>uery<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { GetLeadStatisticsQuery } from '../get-lead-statistics.query';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { LEAD_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';
import { CacheService } from '@/shared/infrastructure/cache';

/**
 * Get Lead Statistics Query Handler
 * Handles retrieving lead statistics with caching support
 */
@Injectable()
@QueryHandler(GetLeadStatisticsQuery)
export class GetLeadStatisticsHandler implements IQueryHandler<GetLeadStatisticsQuery> {
  private readonly logger = new Logger(GetLeadStatisticsHandler.name);
  private readonly CACHE_TTL = 600; // 10 minutes cache for statistics
  private readonly CACHE_PREFIX = 'lead-statistics:';

  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
    private readonly cacheService: CacheService,
  ) {}

  async execute(query: GetLeadStatisticsQuery): Promise<LeadStatistics> {
    const { filters } = query;

    try {
      this.logger.log(`Executing GetLeadStatisticsQuery with filters: ${JSON.stringify(filters)}`);

      // Build cache key based on filters
      const cacheKey = this.buildCacheKey(filters);

      // Try to get from cache first
      const cachedStatistics = await this.cacheService.get<LeadStatistics>(cacheKey);
      if (cachedStatistics) {
        this.logger.log(`Lead statistics found in cache`);
        return cachedStatistics;
      }

      // Use repository's optimized statistics method
      const statistics = await this.leadRepository.getStatistics(filters);

      // Transform to our interface format
      const result: LeadStatistics = {
        totalLeads: statistics.totalLeads,
        totalOpportunities: statistics.totalOpportunities,
        qualifiedLeads: statistics.qualifiedLeads,
        convertedLeads: statistics.convertedLeads,
        averageScore: statistics.averageScore,
        conversionRate: statistics.conversionRate,
        totalRevenue: statistics.totalRevenue,
        weightedRevenue: statistics.weightedRevenue,
        averageDealSize: statistics.averageDealSize,
        averageSalesCycle: statistics.averageSalesCycle,
        winRate: statistics.winRate,
        lossRate: statistics.lossRate,
        pipelineVelocity: statistics.pipelineVelocity,
        byPriority: statistics.byPriority,
        byStage: statistics.byStage,
        byTeam: statistics.byTeam,
        bySource: statistics.bySource,
        generatedAt: new Date().toISOString(),
        filters: filters || {},
      };

      // Cache the result
      await this.cacheService.set(cacheKey, result, this.CACHE_TTL);

      this.logger.log(`Lead statistics calculated and cached successfully`);
      return result;

    } catch (error) {
      this.logger.error(`Failed to execute GetLeadStatisticsQuery`, error);
      throw error;
    }
  }

  /**
   * Build cache key for statistics query
   */
  private buildCacheKey(filters: any): string {
    if (!filters || Object.keys(filters).length === 0) {
      return `${this.CACHE_PREFIX}all`;
    }

    // Sort filters for consistent cache keys
    const sortedFilters = Object.keys(filters)
      .sort()
      .reduce((acc, key) => {
        acc[key] = filters[key];
        return acc;
      }, {} as any);

    return `${this.CACHE_PREFIX}${JSON.stringify(sortedFilters)}`;
  }
}

/**
 * Lead Statistics Interface
 */
export interface LeadStatistics {
  totalLeads: number;
  totalOpportunities: number;
  qualifiedLeads: number;
  convertedLeads: number;
  averageScore: number;
  conversionRate: number;
  totalRevenue: number;
  weightedRevenue: number;
  averageDealSize: number;
  averageSalesCycle: number;
  winRate: number;
  lossRate: number;
  pipelineVelocity: number;
  byPriority: Record<string, number>;
  byStage: Record<string, number>;
  byTeam: Record<string, number>;
  bySource: Record<string, number>;
  generatedAt: string;
  filters: any;
}
