import { QueryHand<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { GetLeadsRequiringAttentionQuery } from '../get-leads-requiring-attention.query';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { LEAD_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';
import { CacheService } from '@/shared/infrastructure/cache';

/**
 * Get Leads Requiring Attention Query Handler
 * Handles retrieving leads that require immediate attention with caching support
 */
@Injectable()
@QueryHandler(GetLeadsRequiringAttentionQuery)
export class GetLeadsRequiringAttentionHandler implements IQueryHandler<GetLeadsRequiringAttentionQuery> {
  private readonly logger = new Logger(GetLeadsRequiringAttentionHandler.name);
  private readonly CACHE_TTL = 300; // 5 minutes cache
  private readonly CACHE_PREFIX = 'attention-leads:';

  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
    private readonly cacheService: CacheService,
  ) {}

  async execute(query: GetLeadsRequiringAttentionQuery): Promise<Lead[]> {
    const { teamId, userId } = query;

    try {
      this.logger.log(`Executing GetLeadsRequiringAttentionQuery`, { teamId, userId });

      // Build cache key
      const cacheKey = this.buildCacheKey(teamId, userId);

      // Try to get from cache first
      const cachedLeads = await this.cacheService.get<Lead[]>(cacheKey);
      if (cachedLeads) {
        this.logger.log(`Found ${cachedLeads.length} leads requiring attention in cache`);
        return cachedLeads;
      }

      // Use repository's optimized method
      const leadsRequiringAttention = await this.leadRepository.findRequiringAttention();

      // Sort by priority and urgency
      const sortedLeads = leadsRequiringAttention.sort((a, b) => {
        // First sort by priority (higher priority first)
        const priorityDiff = (b.priority?.value || 0) - (a.priority?.value || 0);
        if (priorityDiff !== 0) return priorityDiff;

        // Then by expected revenue (higher revenue first)
        const revenueDiff = (b.expectedRevenue || 0) - (a.expectedRevenue || 0);
        if (revenueDiff !== 0) return revenueDiff;

        // Finally by creation date (older first)
        return (a.createdAt?.getTime() || 0) - (b.createdAt?.getTime() || 0);
      });

      // Cache the result
      await this.cacheService.set(cacheKey, sortedLeads, this.CACHE_TTL);

      this.logger.log(`Found ${sortedLeads.length} leads requiring attention`);
      return sortedLeads;

    } catch (error) {
      this.logger.error(`Failed to execute GetLeadsRequiringAttentionQuery`, error);
      throw error;
    }
  }

  /**
   * Build cache key for leads requiring attention query
   */
  private buildCacheKey(teamId?: number, userId?: number): string {
    const parts = [this.CACHE_PREFIX];

    if (teamId) parts.push(`team:${teamId}`);
    if (userId) parts.push(`user:${userId}`);
    if (!teamId && !userId) parts.push('all');

    return parts.join(':');
  }
}
