import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EventBus } from '@nestjs/cqrs';
import { Injectable, Logger, Inject, NotFoundException, BadRequestException } from '@nestjs/common';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { LEAD_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';
import { CacheService } from '@/shared/infrastructure/cache';
import { LeadPriorityUpdatedEvent } from '@/modules/crm/domain/events/lead-priority-updated.event';
import { LeadPriority } from '@/modules/crm/domain/value-objects/lead-priority.vo';

export class UpdateLeadPriorityCommand {
  constructor(
    public readonly leadId: number,
    public readonly priority: number
  ) {}
}

/**
 * Update Lead Priority Command Handler
 * Handles updating lead priority with business validation
 */
@Injectable()
@CommandHandler(UpdateLeadPriorityCommand)
export class UpdateLeadPriorityHandler implements ICommandHandler<UpdateLeadPriorityCommand> {
  private readonly logger = new Logger(UpdateLeadPriorityHandler.name);

  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
    private readonly cacheService: CacheService,
    private readonly eventBus: EventBus,
  ) {}

  async execute(command: UpdateLeadPriorityCommand): Promise<{
    success: boolean;
    leadId: number;
    oldPriority: number;
    newPriority: number;
  }> {
    const { leadId, priority } = command;

    try {
      this.logger.log(`Executing UpdateLeadPriorityCommand for lead ID: ${leadId}, priority: ${priority}`);

      // Validate priority value
      this.validatePriority(priority);

      // Check if lead exists
      const existingLead = await this.leadRepository.findById(leadId);
      if (!existingLead) {
        throw new NotFoundException(`Lead with ID ${leadId} not found`);
      }

      const oldPriority = existingLead.priority?.value || 1;

      // Skip update if priority is the same
      if (oldPriority === priority) {
        this.logger.log(`Priority for lead ${leadId} is already ${priority}, skipping update`);
        return {
          success: true,
          leadId,
          oldPriority,
          newPriority: priority,
        };
      }

      // Perform business validation
      await this.validatePriorityChange(existingLead, oldPriority, priority);

      this.logger.log(`Updating priority for lead ${leadId} from ${oldPriority} to ${priority}`, {
        leadName: existingLead.name,
        leadType: existingLead.type.value,
      });

      // Update priority in repository
      const success = await this.leadRepository.updatePriority(leadId, LeadPriority.fromValue(priority));

      if (!success) {
        throw new Error(`Failed to update priority for lead ${leadId}`);
      }

      // Invalidate related caches
      await this.invalidateRelatedCaches(leadId, existingLead);

      // Publish domain event
      const event = new LeadPriorityUpdatedEvent(
        leadId,
        oldPriority,
        priority,
        existingLead,
        new Date()
      );
      this.eventBus.publish(event);

      this.logger.log(`Priority updated successfully for lead ${leadId}`);

      return {
        success: true,
        leadId,
        oldPriority,
        newPriority: priority,
      };

    } catch (error) {
      this.logger.error(`Failed to execute UpdateLeadPriorityCommand for lead ID: ${leadId}`, error);
      throw error;
    }
  }

  /**
   * Validate priority value
   */
  private validatePriority(priority: number): void {
    if (typeof priority !== 'number' || ![0, 1, 2, 3].includes(priority)) {
      throw new BadRequestException(
        'Priority must be 0 (Low), 1 (Medium), 2 (High), or 3 (Very High)'
      );
    }
  }

  /**
   * Validate business rules for priority changes
   */
  private async validatePriorityChange(lead: any, oldPriority: number, newPriority: number): Promise<void> {
    // Rule: High-value opportunities should not be downgraded to low priority
    if (lead.type.isOpportunity() &&
        (lead.expectedRevenue || 0) > 100000 &&
        newPriority < oldPriority &&
        newPriority === 0) {

      this.logger.warn(`Attempted to set low priority for high-value opportunity ${lead.id}`);
      throw new BadRequestException(
        'High-value opportunities (>$100k) should not be set to low priority'
      );
    }

    // Rule: Overdue leads should maintain at least medium priority
    if (lead.dateDeadline && lead.dateDeadline < new Date() && newPriority === 0) {
      throw new BadRequestException(
        'Overdue leads should maintain at least medium priority'
      );
    }

    // Rule: Log priority escalations for audit
    if (newPriority > oldPriority) {
      this.logger.log(`Priority escalated for lead ${lead.id}: ${oldPriority} -> ${newPriority}`, {
        leadName: lead.name,
        assignedUserId: lead.assignedUserId,
        expectedRevenue: lead.expectedRevenue,
      });
    }

    // Rule: Log priority downgrades for opportunities
    if (newPriority < oldPriority && lead.type.isOpportunity()) {
      this.logger.warn(`Priority downgraded for opportunity ${lead.id}: ${oldPriority} -> ${newPriority}`, {
        leadName: lead.name,
        expectedRevenue: lead.expectedRevenue,
      });
    }
  }

  /**
   * Invalidate related caches after priority update
   */
  private async invalidateRelatedCaches(leadId: number, lead: any): Promise<void> {
    try {
      // Invalidate specific lead cache
      await this.cacheService.del(`lead:${leadId}`);

      // Invalidate priority-related caches
      const cacheKeys = [
        'leads:filters',
        'lead-statistics',
        'attention-leads', // Priority affects attention requirements
        'search:leads'
      ];

      for (const key of cacheKeys) {
        await this.cacheService.del(key);
      }

      this.logger.log(`Invalidated caches for lead ${leadId} priority update`);
    } catch (error) {
      this.logger.error(`Failed to invalidate caches for lead ${leadId}`, error);
      // Don't throw error - cache invalidation failure shouldn't fail the update
    }
  }
}