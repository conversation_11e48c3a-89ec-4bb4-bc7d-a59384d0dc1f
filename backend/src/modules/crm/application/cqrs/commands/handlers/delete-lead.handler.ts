import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON>and<PERSON>, EventBus } from '@nestjs/cqrs';
import { Inject, Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { DeleteLeadCommand } from '../delete-lead.command';
import { ILeadRepository } from "@/modules/crm/domain/repositories/lead.repository";
import { LEAD_REPOSITORY_TOKEN } from "@/modules/crm/domain/repositories/injection-tokens";
import { CacheService } from '@/shared/infrastructure/cache';
import { LeadDeletedEvent } from '@/modules/crm/domain/events/lead-deleted.event';

/**
 * Delete Lead Command Handler
 * Handles the deletion of a lead with business validation
 */
@Injectable()
@CommandHandler(DeleteLeadCommand)
export class DeleteLeadHandler implements ICommandHandler<DeleteLeadCommand> {
  private readonly logger = new Logger(DeleteLeadHandler.name);

  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
    private readonly cacheService: CacheService,
    private readonly eventBus: EventBus,
  ) {}

  async execute(command: DeleteLeadCommand): Promise<{ success: boolean; leadId: number }> {
    const { leadId } = command;

    try {
      this.logger.log(`Executing DeleteLeadCommand for lead ID: ${leadId}`);

      // Check if lead exists
      const lead = await this.leadRepository.findById(leadId);
      if (!lead) {
        throw new NotFoundException(`Lead with ID ${leadId} not found`);
      }

      // Perform business validation before deletion
      await this.validateDeletion(lead);

      this.logger.log(`Deleting lead ${leadId}`, {
        leadName: lead.name,
        leadType: lead.type.value,
        expectedRevenue: lead.expectedRevenue
      });

      // Soft delete the lead (sets active = false in Odoo)
      const deleted = await this.leadRepository.delete(leadId);
      if (!deleted) {
        throw new Error(`Failed to delete lead with ID ${leadId}`);
      }

      // Invalidate related caches
      await this.invalidateRelatedCaches(leadId, lead);

      // Publish domain event
      const event = new LeadDeletedEvent(
        lead.id,
        lead,
        new Date(),
        'manual_deletion' // deletion reason
      );
      this.eventBus.publish(event);

      this.logger.log(`Lead ${leadId} deleted successfully`);

      return {
        success: true,
        leadId: leadId
      };

    } catch (error) {
      this.logger.error(`Failed to execute DeleteLeadCommand for lead ID: ${leadId}`, error);
      throw error;
    }
  }

  /**
   * Validate if lead can be deleted based on business rules
   */
  private async validateDeletion(lead: any): Promise<void> {
    // Rule: Cannot delete opportunities with high expected revenue without approval
    if (lead.type.isOpportunity() && (lead.expectedRevenue || 0) > 50000) {
      this.logger.warn(`Attempted to delete high-value opportunity ${lead.id} (${lead.expectedRevenue})`);
      throw new BadRequestException(
        'Cannot delete high-value opportunities (>$50k) without special approval'
      );
    }

    // Rule: Cannot delete leads that are in advanced stages
    if (lead.stageId && lead.stageId > 4) { // Simplified stage check
      throw new BadRequestException(
        'Cannot delete leads in advanced stages. Please convert or mark as lost instead.'
      );
    }

    // Rule: Cannot delete leads with recent activity (within 24 hours)
    if (lead.updatedAt) {
      const hoursSinceUpdate = (Date.now() - lead.updatedAt.getTime()) / (1000 * 60 * 60);
      if (hoursSinceUpdate < 24) {
        this.logger.warn(`Attempted to delete recently updated lead ${lead.id}`);
        throw new BadRequestException(
          'Cannot delete leads with recent activity. Please wait 24 hours or mark as lost.'
        );
      }
    }

    // Rule: Log deletion of assigned leads for audit
    if (lead.assignedUserId) {
      this.logger.warn(`Deleting assigned lead ${lead.id} (assigned to user ${lead.assignedUserId})`);
    }
  }

  /**
   * Invalidate related caches after deletion
   */
  private async invalidateRelatedCaches(leadId: number, lead: any): Promise<void> {
    try {
      // Invalidate specific lead cache
      await this.cacheService.del(`lead:${leadId}`);

      // Invalidate list caches
      const cacheKeys = [
        'leads:filters',
        'lead-statistics',
        'overdue-leads',
        'attention-leads',
        'search:leads'
      ];

      for (const key of cacheKeys) {
        await this.cacheService.del(key);
      }

      // Invalidate team-specific caches
      if (lead.teamId) {
        await this.cacheService.del(`team:${lead.teamId}`);
      }

      this.logger.log(`Invalidated caches for deleted lead ${leadId}`);
    } catch (error) {
      this.logger.error(`Failed to invalidate caches for deleted lead ${leadId}`, error);
      // Don't throw error - cache invalidation failure shouldn't fail the deletion
    }
  }
}
