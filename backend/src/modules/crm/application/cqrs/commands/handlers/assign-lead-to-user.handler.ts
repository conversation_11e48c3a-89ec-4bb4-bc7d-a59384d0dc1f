import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EventBus } from '@nestjs/cqrs';
import { Inject, Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { AssignLeadToUserCommand } from '../assign-lead-to-user.command';
import { ILeadRepository } from "@/modules/crm/domain/repositories/lead.repository";
import { LEAD_REPOSITORY_TOKEN } from "@/modules/crm/domain/repositories/injection-tokens";
import { CacheService } from '@/shared/infrastructure/cache';
import { LeadAssignedToUserEvent } from '@/modules/crm/domain/events/lead-assigned-to-user.event';

/**
 * Assign Lead to User Command Handler
 * Handles the assignment of a lead to a specific user with business validation
 */
@Injectable()
@CommandHandler(AssignLeadToUserCommand)
export class AssignLeadToUserHandler implements ICommandHandler<AssignLeadToUserCommand> {
  private readonly logger = new Logger(AssignLeadToUserHandler.name);

  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
    private readonly cacheService: CacheService,
    private readonly eventBus: EventBus,
  ) {}

  async execute(command: AssignLeadToUserCommand): Promise<{
    success: boolean;
    leadId: number;
    userId: number;
    previousUserId?: number;
  }> {
    const { leadId, userId, assignedBy } = command;

    try {
      this.logger.log(`Executing AssignLeadToUserCommand for lead ID: ${leadId}, user ID: ${userId}`);

      // Validate user ID
      if (!userId || userId <= 0) {
        throw new BadRequestException('Valid user ID is required');
      }

      // Get the lead
      const lead = await this.leadRepository.findById(leadId);
      if (!lead) {
        throw new NotFoundException(`Lead with ID ${leadId} not found`);
      }

      const previousUserId = lead.assignedUserId;

      // Skip if already assigned to the same user
      if (previousUserId === userId) {
        this.logger.log(`Lead ${leadId} is already assigned to user ${userId}, skipping`);
        return {
          success: true,
          leadId,
          userId,
          previousUserId,
        };
      }

      // Perform business validation
      await this.validateAssignment(lead, userId, assignedBy);

      this.logger.log(`Assigning lead ${leadId} to user ${userId}`, {
        leadName: lead.name,
        previousUserId,
        assignedBy,
      });

      // Assign the lead to user using repository method
      const success = await this.leadRepository.assignToUser(leadId, userId);

      if (!success) {
        throw new Error(`Failed to assign lead ${leadId} to user ${userId}`);
      }

      // Invalidate related caches
      await this.invalidateRelatedCaches(leadId, lead, userId, previousUserId);

      // Publish domain event
      const event = new LeadAssignedToUserEvent(
        leadId,
        userId,
        previousUserId,
        assignedBy,
        lead,
        new Date()
      );
      this.eventBus.publish(event);

      this.logger.log(`Lead ${leadId} assigned to user ${userId} successfully`);

      return {
        success: true,
        leadId,
        userId,
        previousUserId,
      };

    } catch (error) {
      this.logger.error(`Failed to execute AssignLeadToUserCommand for lead ID: ${leadId}`, error);
      throw error;
    }
  }

  /**
   * Validate business rules for lead assignment
   */
  private async validateAssignment(lead: any, userId: number, assignedBy?: number): Promise<void> {
    // Rule: High-value leads should be assigned by managers only
    if ((lead.expectedRevenue || 0) > 100000 && assignedBy) {
      // This would need proper role checking in a real system
      this.logger.log(`High-value lead ${lead.id} being assigned by user ${assignedBy}`);
    }

    // Rule: Cannot assign leads in certain stages to different users without approval
    if (lead.stageId && lead.stageId > 4 && lead.assignedUserId && lead.assignedUserId !== userId) {
      this.logger.warn(`Reassigning advanced-stage lead ${lead.id} from user ${lead.assignedUserId} to ${userId}`);
      // In a real system, this might require approval workflow
    }

    // Rule: Log reassignments for audit
    if (lead.assignedUserId && lead.assignedUserId !== userId) {
      this.logger.log(`Lead reassignment detected`, {
        leadId: lead.id,
        leadName: lead.name,
        fromUserId: lead.assignedUserId,
        toUserId: userId,
        assignedBy,
        expectedRevenue: lead.expectedRevenue,
      });
    }

    // Rule: Validate user exists (simplified - in real system would check user service)
    if (userId <= 0) {
      throw new BadRequestException('Invalid user ID provided');
    }
  }

  /**
   * Invalidate related caches after assignment
   */
  private async invalidateRelatedCaches(
    leadId: number,
    lead: any,
    newUserId: number,
    previousUserId?: number
  ): Promise<void> {
    try {
      // Invalidate specific lead cache
      await this.cacheService.del(`lead:${leadId}`);

      // Invalidate user-specific caches
      const cacheKeys = [
        'leads:filters',
        'lead-statistics',
        'overdue-leads',
        'attention-leads',
        'search:leads'
      ];

      // Add user-specific keys
      if (newUserId) {
        cacheKeys.push(`user:${newUserId}`);
      }
      if (previousUserId) {
        cacheKeys.push(`user:${previousUserId}`);
      }

      for (const key of cacheKeys) {
        await this.cacheService.del(key);
      }

      this.logger.log(`Invalidated caches for lead ${leadId} assignment`);
    } catch (error) {
      this.logger.error(`Failed to invalidate caches for lead ${leadId}`, error);
      // Don't throw error - cache invalidation failure shouldn't fail the assignment
    }
  }
}
