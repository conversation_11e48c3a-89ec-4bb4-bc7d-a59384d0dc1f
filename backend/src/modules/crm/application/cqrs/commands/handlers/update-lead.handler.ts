import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ommandHand<PERSON>, EventBus } from '@nestjs/cqrs';
import { Injectable, Logger, Inject, NotFoundException, BadRequestException } from '@nestjs/common';
import { UpdateLeadCommand } from '../update-lead.command';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { LEAD_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';
import { CacheService } from '@/shared/infrastructure/cache';
import { LeadUpdatedEvent } from '@/modules/crm/domain/events/lead-updated.event';

/**
 * Update Lead Command Handler
 * Handles updating lead information with business validation
 */
@Injectable()
@CommandHandler(UpdateLeadCommand)
export class UpdateLeadHandler implements ICommandHandler<UpdateLeadCommand> {
  private readonly logger = new Logger(UpdateLeadHandler.name);

  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
    private readonly cacheService: CacheService,
    private readonly eventBus: EventBus,
  ) {}

  async execute(command: UpdateLeadCommand): Promise<Lead> {
    const { leadId, updates } = command;

    try {
      this.logger.log(`Executing UpdateLeadCommand for lead ID: ${leadId}`);

      // Validate input data
      this.validateUpdateData(updates);

      // Check if lead exists
      const existingLead = await this.leadRepository.findById(leadId);
      if (!existingLead) {
        throw new NotFoundException(`Lead with ID ${leadId} not found`);
      }

      // Log the update attempt
      this.logger.log(`Updating lead ${leadId}`, {
        leadName: existingLead.name,
        updateFields: Object.keys(updates)
      });

      // Perform business validation
      await this.validateBusinessRules(existingLead, updates);

      // Use repository's update method
      const updatedLead = await this.leadRepository.update(leadId, updates);

      // Invalidate related caches
      await this.invalidateRelatedCaches(leadId, updatedLead);

      // Publish domain event
      const event = new LeadUpdatedEvent(
        updatedLead.id,
        updatedLead,
        existingLead,
        updates,
        Date.now()
      );
      this.eventBus.publish(event);

      this.logger.log(`Lead ${leadId} updated successfully`);

      return updatedLead;

    } catch (error) {
      this.logger.error(`Failed to execute UpdateLeadCommand for lead ID: ${leadId}`, error);
      throw error;
    }
  }

  /**
   * Validate update data structure and types
   */
  private validateUpdateData(updates: any): void {
    if (!updates || Object.keys(updates).length === 0) {
      throw new BadRequestException('Update data cannot be empty');
    }

    // Validate email format if provided
    if (updates.contactInfo?.email && !this.isValidEmail(updates.contactInfo.email)) {
      throw new BadRequestException('Invalid email format');
    }

    // Validate phone format if provided
    if (updates.contactInfo?.phone && !this.isValidPhone(updates.contactInfo.phone)) {
      throw new BadRequestException('Invalid phone format');
    }

    // Validate revenue values
    if (updates.expectedRevenue !== undefined) {
      if (typeof updates.expectedRevenue !== 'number' || updates.expectedRevenue < 0) {
        throw new BadRequestException('Expected revenue must be a positive number');
      }
    }

    // Validate probability
    if (updates.probability !== undefined) {
      if (typeof updates.probability !== 'number' ||
          updates.probability < 0 || updates.probability > 100) {
        throw new BadRequestException('Probability must be between 0 and 100');
      }
    }

    // Validate priority
    if (updates.priority !== undefined) {
      if (![0, 1, 2, 3].includes(updates.priority)) {
        throw new BadRequestException('Priority must be 0 (Low), 1 (Medium), 2 (High), or 3 (Very High)');
      }
    }

    // Validate deadline
    if (updates.dateDeadline !== undefined) {
      const deadline = new Date(updates.dateDeadline);
      if (isNaN(deadline.getTime())) {
        throw new BadRequestException('Invalid deadline date format');
      }
      if (deadline < new Date()) {
        throw new BadRequestException('Deadline cannot be in the past');
      }
    }
  }

  /**
   * Validate business rules for lead updates
   */
  private async validateBusinessRules(existingLead: Lead, updates: any): Promise<void> {
    // Rule: Cannot reduce expected revenue for opportunities in advanced stages
    if (updates.expectedRevenue !== undefined &&
        existingLead.type.isOpportunity() &&
        updates.expectedRevenue < (existingLead.expectedRevenue || 0)) {

      // Check if lead is in advanced stage (this would need proper stage mapping)
      const isAdvancedStage = existingLead.stageId && existingLead.stageId > 3; // Simplified check
      if (isAdvancedStage) {
        this.logger.warn(`Attempted to reduce revenue for advanced opportunity ${existingLead.id}`);
        throw new BadRequestException('Cannot reduce expected revenue for opportunities in advanced stages');
      }
    }

    // Rule: High-value leads must have an assigned user
    if (updates.expectedRevenue !== undefined && updates.expectedRevenue > 100000) {
      if (!updates.assignedUserId && !existingLead.assignedUserId) {
        throw new BadRequestException('High-value leads (>$100k) must be assigned to a user');
      }
    }

    // Rule: Email uniqueness check (if email is being updated)
    if (updates.contactInfo?.email && updates.contactInfo.email !== existingLead.contactInfo.email) {
      const existingLeadWithEmail = await this.leadRepository.findByEmail(updates.contactInfo.email);
      if (existingLeadWithEmail && existingLeadWithEmail.id !== existingLead.id) {
        throw new BadRequestException('A lead with this email already exists');
      }
    }
  }

  /**
   * Invalidate related caches after update
   */
  private async invalidateRelatedCaches(leadId: number, updatedLead: Lead): Promise<void> {
    try {
      // Invalidate specific lead cache
      await this.cacheService.del(`lead:${leadId}`);

      // Invalidate list caches that might include this lead
      const cacheKeys = [
        'leads:filters',
        'lead-statistics',
        'overdue-leads',
        'attention-leads',
        'search:leads'
      ];

      for (const key of cacheKeys) {
        await this.cacheService.del(key);
      }

      // Invalidate team-specific caches if team changed
      if (updatedLead.teamId) {
        await this.cacheService.del(`team:${updatedLead.teamId}`);
      }

      this.logger.log(`Invalidated caches for lead ${leadId}`);
    } catch (error) {
      this.logger.error(`Failed to invalidate caches for lead ${leadId}`, error);
      // Don't throw error - cache invalidation failure shouldn't fail the update
    }
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone format
   */
  private isValidPhone(phone: string): boolean {
    // Simple phone validation - can be enhanced based on requirements
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  }
}
