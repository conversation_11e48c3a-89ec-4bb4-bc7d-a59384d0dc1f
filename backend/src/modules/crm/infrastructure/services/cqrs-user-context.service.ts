import { Injectable, Logger, Scope, Inject } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { JwtService } from '@nestjs/jwt';
import { UserOdooMappingService } from '@/infrastructure/adapters/odoo/user-odoo-mapping.service';
import { OdooConnectionConfig } from '@/shared/domain/value-objects/odoo-connection-config';

export interface CqrsUserContext {
  userId: string;
  sessionId: string;
  odooConfig: OdooConnectionConfig;
  isAuthenticated: boolean;
  jwtPayload?: any;
}

/**
 * CQRS User Context Service
 * Specialized service for extracting and managing user context in CQRS operations
 */
@Injectable({ scope: Scope.REQUEST })
export class CqrsUserContextService {
  private readonly logger = new Logger(CqrsUserContextService.name);
  private userContext: CqrsUserContext | null = null;

  constructor(
    @Inject(REQUEST) private readonly request: Request,
    private readonly jwtService: JwtService,
    private readonly userOdooMappingService: UserOdooMappingService,
  ) {}

  /**
   * Initialize user context from request
   * This should be called at the beginning of each CQRS operation
   */
  async initializeContext(): Promise<CqrsUserContext> {
    if (this.userContext) {
      return this.userContext;
    }

    try {
      // Extract user from request using multiple strategies
      const user = await this.extractUserFromRequest();
      
      if (!user.isAuthenticated) {
        throw new Error('User not authenticated');
      }

      // Get Odoo configuration for the user
      const odooConfig = await this.getOdooConfigForUser(user.userId, user.jwtPayload);

      this.userContext = {
        userId: user.userId,
        sessionId: user.sessionId,
        odooConfig,
        isAuthenticated: true,
        jwtPayload: user.jwtPayload,
      };

      this.logger.debug(`CQRS user context initialized for user: ${user.userId}`);
      return this.userContext;

    } catch (error) {
      this.logger.error('Failed to initialize CQRS user context', error);
      
      // Return anonymous context for graceful degradation
      this.userContext = {
        userId: 'anonymous',
        sessionId: 'anonymous',
        odooConfig: null as any,
        isAuthenticated: false,
      };

      return this.userContext;
    }
  }

  /**
   * Get current user context (initialize if not done)
   */
  async getUserContext(): Promise<CqrsUserContext> {
    if (!this.userContext) {
      return await this.initializeContext();
    }
    return this.userContext;
  }

  /**
   * Extract user information from request with multiple strategies
   */
  private async extractUserFromRequest(): Promise<{
    userId: string;
    sessionId: string;
    isAuthenticated: boolean;
    jwtPayload?: any;
  }> {
    // Strategy 1: From Passport JWT (after guard validation)
    if (this.request.user) {
      const user = this.request.user as any;
      this.logger.debug(`Using Passport JWT user: ${user.id}`);
      
      return {
        userId: user.id,
        sessionId: user.sessionId || this.generateSessionId(),
        isAuthenticated: true,
        jwtPayload: user,
      };
    }

    // Strategy 2: Manual JWT Token Extraction
    const authHeader = this.request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      try {
        const token = authHeader.substring(7);
        const decoded = await this.jwtService.verifyAsync(token);
        
        this.logger.debug(`Using manual JWT extraction: ${decoded.sub}`);
        
        return {
          userId: decoded.sub,
          sessionId: decoded.sessionId || this.generateSessionId(),
          isAuthenticated: true,
          jwtPayload: decoded,
        };
      } catch (error) {
        this.logger.warn('JWT verification failed in manual extraction', error);
      }
    }

    // Strategy 3: Session-based (fallback)
    const requestWithSession = this.request as any;
    if (requestWithSession.session && requestWithSession.session.user) {
      const sessionUser = requestWithSession.session.user;
      this.logger.debug(`Using session user: ${sessionUser.id}`);

      return {
        userId: sessionUser.id,
        sessionId: requestWithSession.sessionID || this.generateSessionId(),
        isAuthenticated: true,
      };
    }

    // No authentication found
    return {
      userId: 'anonymous',
      sessionId: 'anonymous',
      isAuthenticated: false,
    };
  }

  /**
   * Get Odoo configuration for user
   */
  private async getOdooConfigForUser(userId: string, jwtPayload?: any): Promise<OdooConnectionConfig> {
    try {
      // Strategy 1: From JWT payload (most reliable)
      if (jwtPayload && jwtPayload.odooHost && jwtPayload.odooDatabase && jwtPayload.odooUsername) {
        this.logger.debug(`Using Odoo config from JWT for user: ${userId}`);
        
        // Get encrypted password from mapping service
        const mapping = await this.userOdooMappingService.getUserMapping(userId);
        if (!mapping) {
          throw new Error(`No Odoo mapping found for user ${userId}`);
        }
        const decryptedConfig = await this.userOdooMappingService.getDecryptedConfig(mapping);
        
        return {
          host: jwtPayload.odooHost,
          database: jwtPayload.odooDatabase,
          username: jwtPayload.odooUsername,
          password: decryptedConfig.password,
          protocol: 'https',
          port: 443,
        };
      }

      // Strategy 2: From user mapping service
      this.logger.debug(`Fetching Odoo config from mapping service for user: ${userId}`);
      const mapping = await this.userOdooMappingService.getUserMapping(userId);
      if (!mapping) {
        throw new Error(`No Odoo mapping found for user ${userId}`);
      }
      return await this.userOdooMappingService.getDecryptedConfig(mapping);

    } catch (error) {
      this.logger.error(`Failed to get Odoo config for user ${userId}`, error);
      throw new Error(`No Odoo connection found for user ${userId}. Please call connect() first.`);
    }
  }

  /**
   * Generate session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.userContext?.isAuthenticated || false;
  }

  /**
   * Get user ID
   */
  getUserId(): string {
    return this.userContext?.userId || 'anonymous';
  }

  /**
   * Get Odoo configuration
   */
  getOdooConfig(): OdooConnectionConfig | null {
    return this.userContext?.odooConfig || null;
  }

  /**
   * Clear context (for cleanup)
   */
  clearContext(): void {
    this.userContext = null;
  }
}
