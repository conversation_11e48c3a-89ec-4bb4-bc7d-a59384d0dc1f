import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { CqrsUserContextService } from '../services/cqrs-user-context.service';

/**
 * CQRS User Context Interceptor
 * Ensures user context is properly initialized for all CQRS operations
 */
@Injectable()
export class CqrsUserContextInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CqrsUserContextInterceptor.name);

  constructor(
    private readonly cqrsUserContextService: CqrsUserContextService,
  ) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    const startTime = Date.now();

    try {
      // Initialize user context for CQRS operations
      const userContext = await this.cqrsUserContextService.initializeContext();
      
      this.logger.debug(`CQRS user context initialized for ${request.method} ${request.url}`, {
        userId: userContext.userId,
        isAuthenticated: userContext.isAuthenticated,
        hasOdooConfig: !!userContext.odooConfig,
      });

      // Add context info to request for debugging
      request.cqrsUserContext = userContext;

    } catch (error) {
      this.logger.warn(`Failed to initialize CQRS user context for ${request.method} ${request.url}`, error);
      // Don't fail the request, but log the issue
    }

    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - startTime;
        this.logger.debug(`CQRS request completed in ${duration}ms for ${request.method} ${request.url}`);
      }),
      catchError((error) => {
        const duration = Date.now() - startTime;
        this.logger.error(`CQRS request failed after ${duration}ms for ${request.method} ${request.url}`, error);
        
        // Clean up context on error
        try {
          this.cqrsUserContextService.clearContext();
        } catch (cleanupError) {
          this.logger.warn('Failed to clean up CQRS user context', cleanupError);
        }
        
        throw error;
      }),
    );
  }
}
