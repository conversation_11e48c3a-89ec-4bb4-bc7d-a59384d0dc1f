import { DomainEvent } from './base/domain-event.base';
import { Lead } from '../entities/lead.entity';

/**
 * Lead Deleted Domain Event
 * Published when a lead is deleted from the system
 */
export class LeadDeletedEvent extends DomainEvent {
  constructor(
    public readonly leadId: number,
    public readonly deletedLead: Lead,
    public readonly deletedAt: Date,
    public readonly deletionReason: string,
    public readonly deletedBy?: number,
  ) {
    super(
      leadId.toString(),
      'Lead',
      1,
      'lead.deleted',
      {
        businessContext: {
          leadName: deletedLead.name,
          leadType: deletedLead.type.value,
          expectedRevenue: deletedLead.expectedRevenue,
          deletionReason,
        }
      },
      undefined,
      undefined,
      deletedBy?.toString()
    );
  }

  getPayload(): any {
    return {
      leadId: this.leadId,
      leadName: this.deletedLead.name,
      leadType: this.deletedLead.type.value,
      expectedRevenue: this.deletedLead.expectedRevenue,
      assignedUserId: this.deletedLead.assignedUserId,
      teamId: this.deletedLead.teamId,
      deletedAt: this.deletedAt,
      deletionReason: this.deletionReason,
      deletedBy: this.deletedBy,
    };
  }
}
