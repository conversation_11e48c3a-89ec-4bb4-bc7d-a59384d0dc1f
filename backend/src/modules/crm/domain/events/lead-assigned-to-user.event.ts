import { DomainEvent } from './base/domain-event.base';
import { Lead } from '../entities/lead.entity';

/**
 * Lead Assigned to User Domain Event
 * Published when a lead is assigned to a user
 */
export class LeadAssignedToUserEvent extends DomainEvent {
  constructor(
    public readonly leadId: number,
    public readonly assignedUserId: number,
    public readonly previousUserId: number | undefined,
    public readonly assignedBy: number | undefined,
    public readonly lead: Lead,
    public readonly assignedAt: Date,
  ) {
    super(
      leadId.toString(),
      'Lead',
      1,
      'lead.assigned.to.user',
      {
        businessContext: {
          leadName: lead.name,
          leadType: lead.type.value,
          isReassignment: !!previousUserId,
          expectedRevenue: lead.expectedRevenue,
        }
      },
      undefined,
      undefined,
      assignedBy?.toString()
    );
  }

  getPayload(): any {
    return {
      leadId: this.leadId,
      leadName: this.lead.name,
      leadType: this.lead.type.value,
      assignedUserId: this.assignedUserId,
      previousUserId: this.previousUserId,
      assignedBy: this.assignedBy,
      isReassignment: !!this.previousUserId,
      expectedRevenue: this.lead.expectedRevenue,
      priority: this.lead.priority?.value,
      teamId: this.lead.teamId,
      assignedAt: this.assignedAt,
    };
  }
}
