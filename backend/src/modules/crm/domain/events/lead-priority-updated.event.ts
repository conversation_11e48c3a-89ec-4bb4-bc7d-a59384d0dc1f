import { DomainEvent } from './base/domain-event.base';
import { Lead } from '../entities/lead.entity';

/**
 * Lead Priority Updated Domain Event
 * Published when a lead's priority is changed
 */
export class LeadPriorityUpdatedEvent extends DomainEvent {
  constructor(
    public readonly leadId: number,
    public readonly oldPriority: number,
    public readonly newPriority: number,
    public readonly lead: Lead,
    public readonly updatedAt: Date,
    public readonly updatedBy?: number,
  ) {
    super(
      leadId.toString(),
      'Lead',
      1,
      'lead.priority.updated',
      {
        businessContext: {
          leadName: lead.name,
          leadType: lead.type.value,
          priorityChange: newPriority - oldPriority,
          isEscalation: newPriority > oldPriority,
        }
      },
      undefined,
      undefined,
      updatedBy?.toString()
    );
  }

  getPayload(): any {
    return {
      leadId: this.leadId,
      leadName: this.lead.name,
      leadType: this.lead.type.value,
      oldPriority: this.oldPriority,
      newPriority: this.newPriority,
      priorityChange: this.newPriority - this.oldPriority,
      isEscalation: this.newPriority > this.oldPriority,
      isDowngrade: this.newPriority < this.oldPriority,
      expectedRevenue: this.lead.expectedRevenue,
      assignedUserId: this.lead.assignedUserId,
      teamId: this.lead.teamId,
      updatedAt: this.updatedAt,
      updatedBy: this.updatedBy,
    };
  }
}
