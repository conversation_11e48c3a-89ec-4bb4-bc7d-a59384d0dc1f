import {
  Injectable,
  NestInterceptor,
  Exec<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Lo<PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { UserContextService } from '../adapters/odoo/user-context.service';
import { OdooConnectionConfig } from '../../shared/domain/value-objects/odoo-connection-config';

/**
 * User Context Interceptor
 * 
 * This interceptor runs after JWT authentication and ensures that the user context
 * is properly set with Odoo configuration from the JWT payload. This is crucial
 * for session persistence across requests.
 * 
 * The interceptor:
 * 1. Extracts Odoo configuration from the authenticated user (JWT payload)
 * 2. Sets the user context with the configuration
 * 3. Ensures the connection pool can find the user's connection
 */
@Injectable()
export class UserContextInterceptor implements NestInterceptor {
  private readonly logger = new Logger(UserContextInterceptor.name);

  constructor(private readonly userContextService: UserContextService) {}

  async intercept(context: ExecutionContext, next: <PERSON><PERSON><PERSON><PERSON>): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // Only set user context if user is authenticated and has Odoo info
    if (user && user.odooHost && user.odooDatabase && user.odooUsername) {
      try {
        // Reconstruct Odoo config from JWT payload
        const odooConfig: OdooConnectionConfig = {
          host: user.odooHost,
          database: user.odooDatabase,
          username: user.odooUsername,
          password: '', // Will be retrieved from encrypted mapping
          protocol: 'https',
          port: 443,
        };

        // Set user context synchronously before proceeding
        await this.userContextService.setUserContext(odooConfig);
        
        this.logger.debug(`User context set for authenticated user ${user.id} on ${request.method} ${request.url}`);
      } catch (error) {
        this.logger.error(`Failed to set user context for user ${user.id}:`, error);
        // Don't fail the request, just log the error
      }
    }

    return next.handle().pipe(
      tap(() => {
        // Optional: Log successful completion
        if (user) {
          this.logger.debug(`Request completed for user ${user.id}`);
        }
      }),
    );
  }
}
