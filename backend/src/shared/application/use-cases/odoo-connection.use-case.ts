import { Injectable, Logger } from '@nestjs/common';
import {
  OdooConnectionConfig,
  SearchReadOptions,
} from '../../domain/value-objects/odoo-connection-config';
import { OdooConnectionPoolService } from '../../../infrastructure/adapters/odoo/odoo-connection-pool.service';
import { UserContextService } from '../../../infrastructure/adapters/odoo/user-context.service';
import { UserOdooMappingService } from '../../../infrastructure/adapters/odoo/user-odoo-mapping.service';

@Injectable()
export class OdooConnectionUseCase {
  private readonly logger = new Logger(OdooConnectionUseCase.name);

  constructor(
    private readonly connectionPoolService: OdooConnectionPoolService,
    private readonly userContextService: UserContextService,
    private readonly userOdooMappingService: UserOdooMappingService,
  ) {}

  async connect(config: OdooConnectionConfig, odooInstanceId?: string): Promise<void> {
    try {
      // Set user context first to generate enhanced user ID
      await this.userContextService.setUserContext(config);

      // Get the enhanced user ID from the user context
      const userContext = this.userContextService.getUserContext();
      const enhancedUserId = userContext.userId;

      // Create or update user-odoo mapping with enhanced user ID
      const mapping = await this.userOdooMappingService.createMapping({
        webUserId: enhancedUserId,
        odooInstanceId,
        odooConfig: config,
      });

      // Get decrypted config for connection
      const decryptedConfig = await this.userOdooMappingService.getDecryptedConfig(mapping);

      // Update user context with decrypted configuration if needed
      if (decryptedConfig.password !== config.password) {
        await this.userContextService.setUserContext(decryptedConfig);
      }

      // Get or create connection from pool
      const adapter = await this.connectionPoolService.getConnection(
        this.userContextService.getUserContext()
      );

      // Update last used timestamp with enhanced user ID
      await this.userOdooMappingService.updateLastUsed(enhancedUserId, mapping.odooInstanceId);

      this.logger.log(`Successfully connected to Odoo at ${config.host} for web user: ${enhancedUserId}, instance: ${mapping.odooInstanceId}`);
    } catch (error) {
      this.logger.error('Failed to connect to Odoo', error);
      throw error;
    }
  }

  async getVersionInfo() {
    try {
      const adapter = await this.getAdapter();
      return adapter.getVersionInfo();
    } catch (error) {
      this.logger.error('Failed to get version info', error);
      throw error;
    }
  }

  async getCapabilities() {
    try {
      const adapter = await this.getAdapter();
      return adapter.getCapabilities();
    } catch (error) {
      this.logger.error('Failed to get capabilities', error);
      throw error;
    }
  }

  async searchRead<T = any>(
    model: string,
    domain?: any[],
    options?: SearchReadOptions,
  ): Promise<T[]> {
    try {
      const adapter = await this.getAdapter();
      return await adapter.searchRead<T>(model, domain, options);
    } catch (error) {
      this.logger.error(`Failed to search ${model}`, error);
      throw error;
    }
  }

  async create<T = any>(model: string, values: Partial<T>): Promise<number> {
    try {
      const adapter = await this.getAdapter();
      return await adapter.create(model, values);
    } catch (error) {
      this.logger.error(`Failed to create ${model}`, error);
      throw error;
    }
  }

  async update<T = any>(
    model: string,
    ids: number[],
    values: Partial<T>,
  ): Promise<boolean> {
    try {
      const adapter = await this.getAdapter();
      return await adapter.write(model, ids, values);
    } catch (error) {
      this.logger.error(`Failed to update ${model}`, error);
      throw error;
    }
  }

  async delete(model: string, ids: number[]): Promise<boolean> {
    try {
      const adapter = await this.getAdapter();
      return await adapter.unlink(model, ids);
    } catch (error) {
      this.logger.error(`Failed to delete ${model}`, error);
      throw error;
    }
  }

  async execute(
    model: string,
    method: string,
    args: any[],
    kwargs?: any,
  ): Promise<any> {
    try {
      const adapter = await this.getAdapter();
      return await adapter.execute(model, method, args, kwargs);
    } catch (error) {
      this.logger.error(`Failed to execute ${model}.${method}`, error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (this.userContextService.hasUserContext()) {
        await this.connectionPoolService.removeConnection(
          this.userContextService.getUserContext()
        );
        this.logger.log(`Disconnected user: ${this.userContextService.getUserId()} from Odoo`);
      }
    } catch (error) {
      this.logger.error('Failed to disconnect from Odoo', error);
      throw error;
    }
  }

  /**
   * Get pool statistics for monitoring
   */
  getPoolStats() {
    return this.connectionPoolService.getPoolStats();
  }

  /**
   * Get detailed pool metrics
   */
  getPoolMetrics() {
    return this.connectionPoolService.getPoolMetrics();
  }

  /**
   * Health check for all connections
   */
  async healthCheck() {
    return await this.connectionPoolService.healthCheck();
  }

  /**
   * Execute batch operations across multiple users
   */
  async executeBatch<T>(
    operations: Array<{
      userContext: any;
      operation: (adapter: any) => Promise<T>;
    }>,
  ) {
    return await this.connectionPoolService.executeBatchOperations(operations);
  }

  /**
   * Cleanup stale connections
   */
  async cleanupStaleConnections(maxAgeMinutes: number = 60) {
    return await this.connectionPoolService.cleanupStaleConnections(maxAgeMinutes);
  }

  /**
   * Refresh expiring connections
   */
  async refreshExpiringConnections(refreshThresholdMinutes: number = 25) {
    return await this.connectionPoolService.refreshExpiringConnections(refreshThresholdMinutes);
  }

  /**
   * Remove all connections for current user
   */
  async disconnectUser(): Promise<void> {
    try {
      const webUser = this.extractUserFromRequest();

      // Remove all user mappings
      await this.userOdooMappingService.removeUserMappings(webUser.id);

      // Remove all connections from pool
      await this.connectionPoolService.removeUserConnections(webUser.id);

      this.logger.log(`Disconnected all connections for web user: ${webUser.id}`);
    } catch (error) {
      this.logger.error('Failed to disconnect user from Odoo', error);
      throw error;
    }
  }

  /**
   * Get all Odoo instances for the current user
   */
  async getUserOdooInstances(): Promise<Array<{
    instanceId: string;
    host: string;
    database: string;
    username: string;
    isActive: boolean;
    lastUsed: Date;
  }>> {
    try {
      const webUser = this.extractUserFromRequest();
      const mappings = await this.userOdooMappingService.getUserMappings(webUser.id);

      return mappings.map(mapping => ({
        instanceId: mapping.odooInstanceId,
        host: mapping.odooConfig.host,
        database: mapping.odooConfig.database,
        username: mapping.odooConfig.username,
        isActive: mapping.isActive,
        lastUsed: mapping.lastUsed,
      }));
    } catch (error) {
      this.logger.error('Failed to get user Odoo instances', error);
      throw error;
    }
  }

  /**
   * Disconnect from specific Odoo instance
   */
  async disconnectInstance(odooInstanceId: string): Promise<void> {
    try {
      const webUser = this.extractUserFromRequest();

      // Deactivate mapping
      await this.userOdooMappingService.deactivateMapping(webUser.id, odooInstanceId);

      // Remove connection from pool if exists
      const mapping = await this.userOdooMappingService.getUserMapping(webUser.id, odooInstanceId);
      if (mapping) {
        const decryptedConfig = await this.userOdooMappingService.getDecryptedConfig(mapping);
        this.userContextService.setUserContext(decryptedConfig);
        await this.connectionPoolService.removeConnection(this.userContextService.getUserContext());
      }

      this.logger.log(`Disconnected from Odoo instance ${odooInstanceId} for web user: ${webUser.id}`);
    } catch (error) {
      this.logger.error(`Failed to disconnect from instance ${odooInstanceId}`, error);
      throw error;
    }
  }

  /**
   * Private helper to get adapter from pool with enhanced user mapping
   */
  private async getAdapter(odooInstanceId?: string) {
    if (!this.userContextService.hasUserContext()) {
      // Extract web user from request
      const webUser = this.extractUserFromRequest();

      // Try to find user's Odoo mapping
      const mapping = await this.userOdooMappingService.getUserMapping(webUser.id, odooInstanceId);

      if (mapping) {
        // Get decrypted config and set user context
        const decryptedConfig = await this.userOdooMappingService.getDecryptedConfig(mapping);
        this.userContextService.setUserContext(decryptedConfig);

        // Update last used timestamp
        await this.userOdooMappingService.updateLastUsed(webUser.id, mapping.odooInstanceId);

        this.logger.debug(`Restored user context for web user: ${webUser.id}, instance: ${mapping.odooInstanceId}`);
      } else {
        // Enhanced error with debugging information
        const allMappings = await this.userOdooMappingService.getAllUserMappings(webUser.id);
        const debugInfo = {
          requestedUserId: webUser.id,
          requestedInstanceId: odooInstanceId,
          availableMappings: allMappings.map(m => ({
            instanceId: m.odooInstanceId,
            host: m.odooHost,
            database: m.odooDatabase,
            isActive: m.isActive,
            lastUsed: m.lastUsed,
          })),
          totalMappings: allMappings.length,
        };

        this.logger.error('No Odoo connection found - Debug info:', debugInfo);

        const error = new Error(`No Odoo connection found for user ${webUser.id}. Please call connect() first.`);
        (error as any).debugInfo = debugInfo;
        throw error;
      }
    }

    return await this.connectionPoolService.getConnection(
      this.userContextService.getUserContext()
    );
  }

  /**
   * Extract user from request (similar to UserContextService)
   */
  private extractUserFromRequest() {
    // This should match UserContextService logic exactly
    const request = this.userContextService['request'];

    // Method 1: From authenticated user object (JWT)
    if (request?.user) {
      const user = request.user as any; // Type assertion for Passport user
      return {
        id: user.id,
        username: user.username || user.odooUsername,
      };
    }

    // Method 2: From session
    if (request?.session && request.session.user) {
      const sessionUser = request.session.user;
      return {
        id: sessionUser.id,
        username: sessionUser.username,
      };
    }

    // Method 3: From JWT token
    const authHeader = request?.headers?.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      try {
        const token = authHeader.substring(7);
        const decoded = this.decodeJWT(token);
        return {
          id: decoded.sub || decoded.userId,
          username: decoded.username,
        };
      } catch (error) {
        // JWT decode failed, continue to next method
      }
    }

    // Method 4: From custom headers
    const userId = request?.headers?.['x-user-id'] as string;
    const username = request?.headers?.['x-username'] as string;

    if (userId && username) {
      return { id: userId, username };
    }

    // Fallback for development - use same logic as UserContextService
    if (process.env.NODE_ENV === 'development') {
      const ip = request?.ip || request?.connection?.remoteAddress || 'unknown';
      const userAgent = request?.headers?.['user-agent'] || 'unknown';

      // Create a simple hash of IP + User Agent for consistent anonymous ID
      const hash = Buffer.from(`${ip}:${userAgent}`).toString('base64').substr(0, 8);
      const anonymousId = `anon_${hash}`;

      return { id: anonymousId, username: 'anonymous' };
    }

    throw new Error('No user found in request');
  }

  /**
   * Decode JWT token (simplified version)
   */
  private decodeJWT(token: string): any {
    try {
      const payload = token.split('.')[1];
      const decoded = Buffer.from(payload, 'base64').toString('utf-8');
      return JSON.parse(decoded);
    } catch (error) {
      throw new Error('Invalid JWT token');
    }
  }

  /**
   * Delete records from Odoo
   */
  async unlink(model: string, ids: number[]): Promise<boolean> {
    try {
      const connection = await this.getAdapter();
      const result = await connection.unlink(model, ids);
      return result;
    } catch (error) {
      this.logger.error(`Failed to delete records from ${model}:`, error);
      throw error;
    }
  }
}
