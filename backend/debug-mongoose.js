const mongoose = require('mongoose');
require('dotenv').config();

async function debugMongoose() {
  console.log('🚀 Starting Mongoose debug test...');

  // Log environment variables
  console.log('🔍 [DEBUG] Environment variables:');
  console.log('  MONGODB_URI:', process.env.MONGODB_URI);
  console.log('  NODE_ENV:', process.env.NODE_ENV);

  // Log all environment variables that contain 'mongo' or 'db'
  const envVars = Object.keys(process.env).filter(key =>
    key.toLowerCase().includes('mongo') ||
    key.toLowerCase().includes('db') ||
    key.toLowerCase().includes('database')
  );
  console.log('🔍 [DEBUG] All DB-related env vars:', envVars.map(k => `${k}=${process.env[k]}`));

  const uri = process.env.MONGODB_URI || 'mongodb://127.0.0.1:27018/test-debug';
  console.log('🔍 [DEBUG] Final URI:', uri);

  const config = {
    family: 4,
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 5000,
    directConnection: true, // Bypass replica set discovery
  };

  console.log('🔍 [DEBUG] Mongoose config:', JSON.stringify(config, null, 2));

  try {
    console.log('🔗 Attempting to connect to MongoDB...');

    // Enable Mongoose debugging
    mongoose.set('debug', true);

    await mongoose.connect(uri, config);
    console.log('✅ Connected successfully to MongoDB!');

    await mongoose.disconnect();
    console.log('✅ Test completed successfully');
    process.exit(0);

  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

debugMongoose();
