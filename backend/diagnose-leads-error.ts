#!/usr/bin/env ts-node

import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { OdooModule } from './src/infrastructure/odoo.module';
import { OdooConnectionPoolService, UserContext } from './src/infrastructure/adapters/odoo/odoo-connection-pool.service';
import { OdooConnectionConfig } from './src/shared/domain/value-objects/odoo-connection-config';

const logger = new Logger('OdooLeadsDiagnosis');

async function diagnoseLeadsError() {
  logger.log('🔍 Starting Odoo Leads Error Diagnosis...');
  
  try {
    // Create NestJS application context
    const app = await NestFactory.createApplicationContext(OdooModule);
    const connectionPool = app.get(OdooConnectionPoolService);
    
    // Initialize the connection pool
    await connectionPool.onModuleInit();
    
    // Test credentials from your request
    const testConfig: OdooConnectionConfig = {
      host: 'https://odoo18.bestmix.one/',
      database: 'bestmix_27_6',
      username: 'tuan.le',
      password: 'drb6mtw3bah8byu*VEV',
      preferredProtocol: 'jsonrpc',
      protocol: 'https',
      port: 443,
    } as any;
    
    const userContext: UserContext = {
      userId: 'test-user-tuan-diagnosis',
      sessionId: `diagnosis-session-${Date.now()}`,
      odooConfig: testConfig,
    };
    
    logger.log('📡 Step 1: Testing basic connection...');
    const adapter = await connectionPool.getConnection(userContext);
    logger.log('✅ Connection established successfully');
    
    logger.log('🔐 Step 2: Testing authentication...');
    const versionInfo = adapter.getVersionInfo();
    logger.log(`✅ Authenticated to Odoo ${versionInfo?.series} (${versionInfo?.edition})`);

    logger.log('👤 Step 3: Testing user permissions...');
    try {
      const userInfo = await adapter.searchRead('res.users', [['login', '=', 'tuan.le']], { limit: 1 });
      logger.log(`✅ User found: ${JSON.stringify(userInfo[0], null, 2)}`);
    } catch (error) {
      logger.error('❌ Failed to get user info:', error.message);
    }

    logger.log('🏢 Step 4: Testing CRM model access...');
    try {
      // Test if we can access crm.lead model at all
      const modelAccess = await adapter.execute('ir.model.access', 'check', ['crm.lead', 'read']);
      logger.log(`✅ CRM Lead model access: ${modelAccess}`);
    } catch (error) {
      logger.error('❌ CRM Lead model access denied:', error.message);
    }

    logger.log('📊 Step 5: Testing simple lead count...');
    try {
      const leadIds = await adapter.execute('crm.lead', 'search', [[]]);
      logger.log(`✅ Total leads in system: ${leadIds.length}`);
    } catch (error) {
      logger.error('❌ Failed to count leads:', error.message);
    }
    
    logger.log('🔍 Step 6: Testing field access...');
    const testFields = [
      'id', 'name', 'email_from', 'phone', 'partner_name', 
      'stage_id', 'user_id', 'team_id', 'create_date'
    ];
    
    for (const field of testFields) {
      try {
        const result = await adapter.searchRead('crm.lead', [], { 
          fields: [field], 
          limit: 1 
        });
        logger.log(`✅ Field '${field}': accessible`);
      } catch (error) {
        logger.error(`❌ Field '${field}': ${error.message}`);
      }
    }
    
    logger.log('📋 Step 7: Testing the exact query from LeadsService...');
    const LEAD_FIELDS = [
      'id', 'name', 'email_from', 'phone', 'partner_name', 'contact_name',
      'street', 'city', 'country_id', 'website', 'stage_id', 'type', 'priority',
      'expected_revenue', 'probability', 'description', 'user_id', 'team_id',
      'company_id', 'partner_id', 'date_deadline', 'lost_reason_id',
      'campaign_id', 'source_id', 'medium_id', 'tag_ids', 'create_date',
      'write_date', 'active'
    ];
    
    try {
      const domain = [['active', '=', true]];
      const options = {
        fields: LEAD_FIELDS,
        offset: 0,
        limit: 20,
        order: 'create_date desc'
      };
      
      logger.log(`🔍 Testing exact query: searchRead('crm.lead', ${JSON.stringify(domain)}, ${JSON.stringify(options)})`);
      const leads = await adapter.searchRead('crm.lead', domain, options);
      logger.log(`✅ Query successful! Found ${leads.length} leads`);
      
      if (leads.length > 0) {
        logger.log(`📄 Sample lead data: ${JSON.stringify(leads[0], null, 2)}`);
      }
      
    } catch (error) {
      logger.error('❌ Exact query failed:', error.message);
      logger.error('Full error:', error);
      
      // Try with minimal fields
      logger.log('🔄 Trying with minimal fields...');
      try {
        const minimalResult = await adapter.searchRead('crm.lead', [['active', '=', true]], { 
          fields: ['id', 'name'], 
          limit: 5 
        });
        logger.log(`✅ Minimal query successful! Found ${minimalResult.length} leads`);
      } catch (minError) {
        logger.error('❌ Even minimal query failed:', minError.message);
      }
    }
    
    logger.log('🔍 Step 8: Testing individual problematic fields...');
    const problematicFields = [
      'country_id', 'stage_id', 'user_id', 'team_id', 'company_id', 
      'partner_id', 'lost_reason_id', 'campaign_id', 'source_id', 
      'medium_id', 'tag_ids'
    ];
    
    for (const field of problematicFields) {
      try {
        const result = await adapter.searchRead('crm.lead', [['active', '=', true]], { 
          fields: ['id', field], 
          limit: 1 
        });
        logger.log(`✅ Relational field '${field}': accessible`);
      } catch (error) {
        logger.error(`❌ Relational field '${field}': ${error.message}`);
      }
    }
    
    logger.log('🎯 Step 9: Testing connection pool info...');
    try {
      // Test if connection is still active
      const testResult = await adapter.searchRead('res.users', [['id', '=', 1]], { limit: 1 });
      logger.log(`📊 Connection still active: ${testResult.length > 0 ? 'Yes' : 'No'}`);
    } catch (error) {
      logger.error('❌ Connection test failed:', error.message);
    }
    
    await app.close();
    logger.log('🏁 Diagnosis completed successfully');
    
  } catch (error) {
    logger.error('💥 Diagnosis failed:', error);
    logger.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the diagnosis
diagnoseLeadsError().catch(console.error);
