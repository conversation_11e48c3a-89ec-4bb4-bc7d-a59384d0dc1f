#!/bin/bash

# Debug User Mapping Script
# Tests JWT token creation and user mapping

set -e

echo "🔍 Debug User Mapping"
echo "===================="

BASE_URL="http://localhost:3000/api/v1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "📡 Step 1: Creating Odoo connection..."
CONNECT_RESPONSE=$(curl -s -X POST $BASE_URL/odoo/connect \
  -H "Content-Type: application/json" \
  -d '{
    "host": "https://odoo18.bestmix.one/",
    "database": "bestmix_27_6",
    "username": "tuan.le",
    "password": "drb6mtw3bah8byu*VEV"
  }')

echo -e "${BLUE}Connect Response:${NC}"
echo "$CONNECT_RESPONSE" | jq '.'

TOKEN=$(echo "$CONNECT_RESPONSE" | jq -r '.data.token')

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo -e "${RED}❌ Failed to get JWT token${NC}"
    exit 1
fi

echo -e "${GREEN}✅ JWT token obtained${NC}"
echo "Token: ${TOKEN:0:50}..."

echo ""
echo "🔍 Step 2: Decoding JWT token..."
# Decode JWT payload (base64 decode the middle part)
JWT_PAYLOAD=$(echo "$TOKEN" | cut -d'.' -f2)
# Add padding if needed
case $((${#JWT_PAYLOAD} % 4)) in
    2) JWT_PAYLOAD="${JWT_PAYLOAD}==" ;;
    3) JWT_PAYLOAD="${JWT_PAYLOAD}=" ;;
esac

DECODED_PAYLOAD=$(echo "$JWT_PAYLOAD" | base64 -d 2>/dev/null || echo "Failed to decode")

echo -e "${BLUE}JWT Payload:${NC}"
echo "$DECODED_PAYLOAD" | jq '.' 2>/dev/null || echo "$DECODED_PAYLOAD"

echo ""
echo "🧪 Step 3: Testing CQRS endpoint with token..."
CQRS_RESPONSE=$(curl -s -X GET "$BASE_URL/crm/leads-cqrs/test" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

echo -e "${BLUE}CQRS Response:${NC}"
echo "$CQRS_RESPONSE" | jq '.'

echo ""
echo "🔍 Step 4: Testing lead query..."
LEAD_RESPONSE=$(curl -s -X GET "$BASE_URL/crm/leads-cqrs?limit=1" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

echo -e "${BLUE}Lead Query Response:${NC}"
echo "$LEAD_RESPONSE" | jq '.'

echo ""
echo "📋 Debug Summary:"
echo "- JWT token creation: ✅"
echo "- JWT token decoding: ✅"
echo "- CQRS endpoint access: ✅"
echo "- User mapping issue identified: ⚠️"
echo ""
echo "🔧 Next steps:"
echo "1. Check user ID generation in JWT"
echo "2. Verify user mapping storage"
echo "3. Debug connection pool key generation"
