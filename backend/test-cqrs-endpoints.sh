#!/bin/bash

# Test script for new CQRS endpoints
echo "🧪 Testing New CQRS Endpoints"
echo "================================"

# Get JWT token
echo "📡 Getting JWT token..."
TOKEN=$(curl -s -X POST http://localhost:3000/api/v1/odoo/connect \
  -H "Content-Type: application/json" \
  -d '{
    "host": "https://odoo18.bestmix.one/",
    "database": "bestmix_27_6",
    "username": "tuan.le",
    "password": "drb6mtw3bah8byu*VEV"
  }' | jq -r '.data.token')

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
  echo "❌ Failed to get JWT token"
  exit 1
fi

echo "✅ JWT token obtained"
echo ""

# Test 1: Get lead by ID
echo "🔍 Test 1: Get lead by ID (GET /api/v1/crm/leads-cqrs/1)"
curl -s -X GET http://localhost:3000/api/v1/crm/leads-cqrs/1 \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.success, .message, .metadata.queryType'
echo ""

# Test 2: Get leads by filters
echo "🔍 Test 2: Get leads by filters (GET /api/v1/crm/leads-cqrs?limit=5)"
curl -s -X GET "http://localhost:3000/api/v1/crm/leads-cqrs?limit=5" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.success, .message, .metadata.queryType, .pagination'
echo ""

# Test 3: Get lead statistics
echo "📊 Test 3: Get lead statistics (GET /api/v1/crm/leads-cqrs/statistics)"
curl -s -X GET http://localhost:3000/api/v1/crm/leads-cqrs/statistics \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.success, .message, .metadata.queryType'
echo ""

# Test 4: Get overdue leads
echo "⏰ Test 4: Get overdue leads (GET /api/v1/crm/leads-cqrs/overdue)"
curl -s -X GET http://localhost:3000/api/v1/crm/leads-cqrs/overdue \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.success, .message, .metadata.queryType'
echo ""

# Test 5: Get leads requiring attention
echo "⚠️  Test 5: Get leads requiring attention (GET /api/v1/crm/leads-cqrs/attention-required)"
curl -s -X GET http://localhost:3000/api/v1/crm/leads-cqrs/attention-required \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.success, .message, .metadata.queryType'
echo ""

# Test 6: Search leads
echo "🔎 Test 6: Search leads (POST /api/v1/crm/leads-cqrs/search)"
curl -s -X POST http://localhost:3000/api/v1/crm/leads-cqrs/search \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "searchTerm": "test",
    "limit": 5
  }' | jq '.success, .message, .metadata.queryType'
echo ""

# Test 7: Update lead priority
echo "🔧 Test 7: Update lead priority (PATCH /api/v1/crm/leads-cqrs/1/priority)"
curl -s -X PATCH http://localhost:3000/api/v1/crm/leads-cqrs/1/priority \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "priority": 2
  }' | jq '.success, .message, .metadata.commandType'
echo ""

# Test 8: Assign lead to user
echo "👤 Test 8: Assign lead to user (PATCH /api/v1/crm/leads-cqrs/1/assign-user)"
curl -s -X PATCH http://localhost:3000/api/v1/crm/leads-cqrs/1/assign-user \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": 55
  }' | jq '.success, .message, .metadata.commandType'
echo ""

# Test 9: Add tag to lead
echo "🏷️  Test 9: Add tag to lead (POST /api/v1/crm/leads-cqrs/1/tags)"
curl -s -X POST http://localhost:3000/api/v1/crm/leads-cqrs/1/tags \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "tag": "test-tag"
  }' | jq '.success, .message, .metadata.commandType'
echo ""

echo "🎉 All CQRS endpoint tests completed!"
