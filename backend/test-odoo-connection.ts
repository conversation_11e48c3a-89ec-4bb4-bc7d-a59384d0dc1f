#!/usr/bin/env ts-node

import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { SharedModule } from './src/shared/shared.module';
import { OdooConnectionPoolService } from './src/infrastructure/adapters/odoo/odoo-connection-pool.service';
import { UniversalOdooAdapter } from './src/infrastructure/adapters/odoo/universal-odoo-adapter';
import { OdooConnectionConfig } from './src/shared/domain/value-objects/odoo-connection-config';

interface UserContext {
  userId: string;
  sessionId: string;
  odooConfig: OdooConnectionConfig;
}

async function testOdooConnection() {
  const logger = new Logger('OdooConnectionTest');
  
  try {
    logger.log('🚀 Starting Odoo Connection Test...');
    
    // Create NestJS application context
    const app = await NestFactory.createApplicationContext(SharedModule);
    const connectionPool = app.get(OdooConnectionPoolService);
    
    // Initialize the connection pool
    await connectionPool.onModuleInit();
    
    // Test credentials - Updated with your provided credentials
    const testConfig: OdooConnectionConfig = {
      host: 'https://odoo18.bestmix.one/',
      database: 'bestmix_27_6',
      username: 'tuan.le',
      password: 'drb6mtw3bah8byu*VEV',
      preferredProtocol: 'jsonrpc', // Force JSON-RPC for testing
    } as any;
    
    const userContext: UserContext = {
      userId: 'test-user-tuan',
      sessionId: `test-session-${Date.now()}`,
      odooConfig: testConfig,
    };
    
    logger.log('📡 Testing connection to Odoo instance...');
    logger.log(`Host: ${testConfig.host}`);
    logger.log(`Database: ${testConfig.database}`);
    logger.log(`Username: ${testConfig.username}`);
    
    // Test 1: Get connection from pool
    logger.log('\n=== Test 1: Connection Pool ===');
    const startTime = Date.now();
    
    const adapter = await connectionPool.getConnection(userContext);
    const connectionTime = Date.now() - startTime;
    
    logger.log(`✅ Connection established in ${connectionTime}ms`);
    
    // Test 2: Get version info
    logger.log('\n=== Test 2: Version Detection ===');
    const versionInfo = adapter.getVersionInfo();
    logger.log(`Odoo Version: ${versionInfo?.series} (${versionInfo?.edition})`);
    logger.log(`Major Version: ${versionInfo?.major}`);
    
    // Test 3: Get capabilities
    logger.log('\n=== Test 3: Capabilities ===');
    const capabilities = adapter.getCapabilities();
    logger.log(`JSON-RPC Support: ${capabilities?.hasJsonRpc ? '✅' : '❌'}`);
    logger.log(`REST API Support: ${capabilities?.hasRestApi ? '✅' : '❌'}`);
    logger.log(`Supported Auth Methods: ${capabilities?.supportedAuthMethods?.join(', ') || 'N/A'}`);
    
    // Test 4: Basic data operations
    logger.log('\n=== Test 4: Data Operations ===');
    
    // Test search_read on res.users (current user)
    const currentUser = await adapter.searchRead('res.users', [['id', '=', 1]], { 
      fields: ['id', 'name', 'login', 'email'],
      limit: 1 
    });
    logger.log(`Current User: ${JSON.stringify(currentUser[0], null, 2)}`);
    
    // Test search_read on res.partner (customers)
    const partners = await adapter.searchRead('res.partner', [['is_company', '=', true]], { 
      fields: ['id', 'name', 'email', 'phone'],
      limit: 5 
    });
    logger.log(`Found ${partners.length} companies:`);
    partners.forEach((partner, index) => {
      logger.log(`  ${index + 1}. ${partner.name} (ID: ${partner.id})`);
    });
    
    // Test search_read on product.product
    const products = await adapter.searchRead('product.product', [['sale_ok', '=', true]], { 
      fields: ['id', 'name', 'default_code', 'list_price'],
      limit: 5 
    });
    logger.log(`Found ${products.length} saleable products:`);
    products.forEach((product, index) => {
      logger.log(`  ${index + 1}. ${product.name} - $${product.list_price} (Code: ${product.default_code || 'N/A'})`);
    });
    
    // Test 5: Pool health check
    logger.log('\n=== Test 5: Pool Health Check ===');
    const healthCheck = await connectionPool.healthCheck();
    logger.log(`Total Connections: ${healthCheck.totalConnections}`);
    logger.log(`Healthy Connections: ${healthCheck.healthyConnections}`);
    logger.log(`Unhealthy Connections: ${healthCheck.unhealthyConnections}`);
    
    // Test 6: Pool metrics
    logger.log('\n=== Test 6: Pool Metrics ===');
    const metrics = connectionPool.getPoolMetrics();
    logger.log(`Pool Size: ${metrics.size}/${metrics.maxSize} (${metrics.utilizationPercent.toFixed(1)}%)`);
    logger.log(`Total Usage Count: ${metrics.totalUsageCount}`);
    logger.log(`Average Usage Count: ${metrics.averageUsageCount.toFixed(1)}`);
    logger.log(`Healthy Connections: ${metrics.healthyConnections}`);
    
    // Test 7: Performance test - multiple operations
    logger.log('\n=== Test 7: Performance Test ===');
    const perfStartTime = Date.now();
    
    const performancePromises = [
      adapter.searchRead('res.users', [], { limit: 10 }),
      adapter.searchRead('res.partner', [['customer_rank', '>', 0]], { limit: 10 }),
      adapter.searchRead('product.product', [], { limit: 10 }),
      adapter.searchRead('sale.order', [], { limit: 5 }),
      adapter.searchRead('account.move', [], { limit: 5 }),
    ];
    
    const results = await Promise.allSettled(performancePromises);
    const perfDuration = Date.now() - perfStartTime;
    
    const successCount = results.filter(r => r.status === 'fulfilled').length;
    logger.log(`Performance Test: ${successCount}/${results.length} operations succeeded in ${perfDuration}ms`);
    
    results.forEach((result, index) => {
      const operations = ['Users', 'Customers', 'Products', 'Sales Orders', 'Invoices'];
      if (result.status === 'fulfilled') {
        logger.log(`  ✅ ${operations[index]}: ${result.value.length} records`);
      } else {
        logger.log(`  ❌ ${operations[index]}: ${result.reason.message}`);
      }
    });
    
    // Test 8: Connection reuse
    logger.log('\n=== Test 8: Connection Reuse ===');
    const reuseStartTime = Date.now();
    const adapter2 = await connectionPool.getConnection(userContext);
    const reuseTime = Date.now() - reuseStartTime;
    
    logger.log(`Connection reuse time: ${reuseTime}ms`);
    logger.log(`Same adapter instance: ${adapter === adapter2 ? '✅' : '❌'}`);

    // Test 9: CRM Leads Testing (The main issue we're diagnosing)
    logger.log('\n=== Test 9: CRM Leads Testing ===');

    try {
      // Test basic lead count
      logger.log('📊 Testing lead count...');
      const leadIds = await adapter.execute('crm.lead', 'search', [[]]);
      logger.log(`✅ Total leads in system: ${leadIds.length}`);

      // Test individual field access
      logger.log('🔍 Testing individual field access...');
      const testFields = ['id', 'name', 'email_from', 'phone', 'partner_name', 'stage_id', 'user_id', 'team_id'];

      for (const field of testFields) {
        try {
          const result = await adapter.searchRead('crm.lead', [], { fields: [field], limit: 1 });
          logger.log(`✅ Field '${field}': accessible (${result.length} records)`);
        } catch (error) {
          logger.error(`❌ Field '${field}': ${error.message}`);
        }
      }

      // Test the exact query from LeadsService
      logger.log('📋 Testing exact LeadsService query...');
      const LEAD_FIELDS = [
        'id', 'name', 'email_from', 'phone', 'partner_name', 'contact_name',
        'street', 'city', 'country_id', 'website', 'stage_id', 'type', 'priority',
        'expected_revenue', 'probability', 'description', 'user_id', 'team_id',
        'company_id', 'partner_id', 'date_deadline', 'lost_reason_id',
        'campaign_id', 'source_id', 'medium_id', 'tag_ids', 'create_date',
        'write_date', 'active'
      ];

      const domain = [['active', '=', true]];
      const options = {
        fields: LEAD_FIELDS,
        offset: 0,
        limit: 20,
        order: 'create_date desc'
      };

      logger.log(`🔍 Executing: searchRead('crm.lead', ${JSON.stringify(domain)}, ${JSON.stringify(options)})`);
      const leads = await adapter.searchRead('crm.lead', domain, options);
      logger.log(`✅ Query successful! Found ${leads.length} leads`);

      if (leads.length > 0) {
        logger.log(`📄 Sample lead data: ${JSON.stringify(leads[0], null, 2)}`);
      }

    } catch (error) {
      logger.error('❌ CRM Leads test failed:', error.message);
      logger.error('Full error:', error);

      // Try with minimal fields
      logger.log('🔄 Trying with minimal fields...');
      try {
        const minimalResult = await adapter.searchRead('crm.lead', [['active', '=', true]], {
          fields: ['id', 'name'],
          limit: 5
        });
        logger.log(`✅ Minimal query successful! Found ${minimalResult.length} leads`);
      } catch (minError) {
        logger.error('❌ Even minimal query failed:', minError.message);
      }
    }

    // Final summary
    logger.log('\n=== 🎉 Test Summary ===');
    logger.log('✅ Connection established successfully');
    logger.log('✅ Version detection working');
    logger.log('✅ Capabilities detection working');
    logger.log('✅ Data operations working');
    logger.log('✅ Pool health monitoring working');
    logger.log('✅ Performance metrics working');
    logger.log('✅ Connection reuse working');
    
    logger.log('\n🚀 All tests passed! Odoo connection is working perfectly.');
    
    // Cleanup
    await connectionPool.onModuleDestroy();
    await app.close();
    
  } catch (error) {
    logger.error('❌ Test failed:', error);
    logger.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testOdooConnection()
    .then(() => {
      console.log('\n✅ Test completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

export { testOdooConnection };
